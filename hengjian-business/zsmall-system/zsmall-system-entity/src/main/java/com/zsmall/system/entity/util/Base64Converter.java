package com.zsmall.system.entity.util;

import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.Base64;

public class Base64Converter {

    /**
     * 将Base64字符串转换为MultipartFile
     * @param base64Data 完整的Base64字符串（可包含前缀）
     * @param fileName 文件名（带扩展名）
     * @return MultipartFile对象
     */
    public static MultipartFile toMultipartFile(String base64Data, String fileName) {
        // 自动清理Base64前缀
        String cleanBase64 = cleanBase64Data(base64Data);

        // 使用Java标准库解码Base64
        byte[] decoded = Base64.getDecoder().decode(cleanBase64);

        // 自动检测内容类型
        String contentType = detectContentType(fileName);

        // 创建MultipartFile
        return new MockMultipartFile(
            "file",          // 表单字段名
            fileName,        // 文件名
            contentType,     // 内容类型
            decoded          // 文件内容
        );
    }

    /**
     * 清理Base64数据（移除可能的URI前缀）
     */
    private static String cleanBase64Data(String base64Data) {
        if (base64Data == null || base64Data.isEmpty()) {
            throw new IllegalArgumentException("Base64数据不能为空");
        }

        // 移除数据URI前缀（如"data:application/pdf;base64,"）
        if (base64Data.contains(";base64,")) {
            return base64Data.split(";base64,")[1];
        }
        return base64Data;
    }

    /**
     * 根据文件扩展名检测内容类型
     */
    private static String detectContentType(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "application/octet-stream";
        }

        String lowerName = fileName.toLowerCase();

        if (lowerName.endsWith(".pdf")) return "application/pdf";
        if (lowerName.endsWith(".jpg") || lowerName.endsWith(".jpeg")) return "image/jpeg";
        if (lowerName.endsWith(".png")) return "image/png";
        if (lowerName.endsWith(".gif")) return "image/gif";
        if (lowerName.endsWith(".txt")) return "text/plain";
        if (lowerName.endsWith(".html") || lowerName.endsWith(".htm")) return "text/html";
        if (lowerName.endsWith(".csv")) return "text/csv";
        if (lowerName.endsWith(".json")) return "application/json";
        if (lowerName.endsWith(".xml")) return "application/xml";
        if (lowerName.endsWith(".zip")) return "application/zip";
        if (lowerName.endsWith(".doc") || lowerName.endsWith(".docx")) return "application/msword";
        if (lowerName.endsWith(".xls") || lowerName.endsWith(".xlsx")) return "application/vnd.ms-excel";
        if (lowerName.endsWith(".ppt") || lowerName.endsWith(".pptx")) return "application/vnd.ms-powerpoint";

        return "application/octet-stream";
    }

    /**
     * 带验证的安全转换方法
     */
    public static MultipartFile toMultipartFileSafe(String base64Data, String fileName) {
        try {
            // 验证输入
            if (base64Data == null || base64Data.isEmpty()) {
                throw new IllegalArgumentException("Base64数据不能为空");
            }
            if (fileName == null || fileName.isEmpty()) {
                throw new IllegalArgumentException("文件名不能为空");
            }

            // 清理和解码
            String cleanBase64 = cleanBase64Data(base64Data);
            byte[] decoded = Base64.getDecoder().decode(cleanBase64);

            // 验证文件签名（可选）
            if (!isValidFileSignature(decoded, fileName)) {
                throw new IllegalArgumentException("文件内容与类型不匹配");
            }

            // 创建MultipartFile
            return new MockMultipartFile(
                "file",
                fileName,
                detectContentType(fileName),
                decoded
            );
        } catch (IllegalArgumentException e) {
            throw new Base64ConversionException("无效的Base64格式: " + e.getMessage(), e);
        }
    }

    /**
     * 验证文件签名（可选增强功能）
     */
    private static boolean isValidFileSignature(byte[] data, String fileName) {
        if (data.length < 4) return false;

        String lowerName = fileName.toLowerCase();

        // PDF签名验证: %PDF
        if (lowerName.endsWith(".pdf")) {
            return data[0] == 0x25 && // %
                data[1] == 0x50 && // P
                data[2] == 0x44 && // D
                data[3] == 0x46;   // F
        }

        // JPEG签名验证: FF D8 FF
        if (lowerName.endsWith(".jpg") || lowerName.endsWith(".jpeg")) {
            return data[0] == (byte) 0xFF &&
                data[1] == (byte) 0xD8 &&
                data[2] == (byte) 0xFF;
        }

        // PNG签名验证: 89 50 4E 47 0D 0A 1A 0A
        if (lowerName.endsWith(".png")) {
            return data.length > 8 &&
                data[0] == (byte) 0x89 &&
                data[1] == 0x50 && // P
                data[2] == 0x4E && // N
                data[3] == 0x47 && // G
                data[4] == 0x0D &&
                data[5] == 0x0A &&
                data[6] == 0x1A &&
                data[7] == 0x0A;
        }

        // 其他文件类型可以添加更多验证...
        return true; // 默认通过验证
    }

    /**
     * 自定义异常类
     */
    public static class Base64ConversionException extends RuntimeException {
        public Base64ConversionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
