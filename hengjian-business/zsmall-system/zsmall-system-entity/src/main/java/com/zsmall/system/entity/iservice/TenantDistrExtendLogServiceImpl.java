package com.zsmall.system.entity.iservice;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.system.entity.domain.TenantDistrExtendLog;
import com.zsmall.system.entity.mapper.TenantDistrExtendLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tenant_distr_extend_log(租户分销商-拓展信息日志表)】的数据库操作Service实现
* @createDate 2025-08-05 15:10:45
*/
@Service
public class TenantDistrExtendLogServiceImpl extends ServiceImpl<TenantDistrExtendLogMapper, TenantDistrExtendLog>
    implements TenantDistrExtendLogService{

    @Override
    public TenantDistrExtendLog getByTenantIdNoTenant(String tenantId) {
        LambdaQueryWrapper<TenantDistrExtendLog> logLambdaQueryWrapper = new LambdaQueryWrapper<>();
        logLambdaQueryWrapper.eq(TenantDistrExtendLog::getTenantId,tenantId);
        logLambdaQueryWrapper.eq(TenantDistrExtendLog::getDelFlag,0);
        return TenantHelper.ignore(()->baseMapper.selectOne(logLambdaQueryWrapper));
    }
}




