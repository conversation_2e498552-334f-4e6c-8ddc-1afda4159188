package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/8/5 13:43
 */
@Data
@TableName("tenant_distr_extend_log")
public class TenantDistrExtendLog implements Serializable {

    /**
     * 自增id
     */
    @TableId(value = "id")
    private Long id;

    private Boolean pushResult;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    private String tenantId;
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
