package com.zsmall.common.enums.order;

import java.util.Optional;
import java.util.Set;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/7/9 10:14
 */

public enum OrderStatusType {
    FULFILLMENT_PROGRESS(Set.of("Dispatched", "UnDispatched", "Abnormal")),
    ORDER_STATE(Set.of("Paid", "Failed", "Canceled", "UnPaid", "Pending", "Refunded", "Verifying"));

    private final Set<String> values;

    // 私有构造方法
    private OrderStatusType(Set<String> values) {
        this.values = values;
    }

    // 获取枚举包含的状态值
    public Set<String> getValues() {
        return values;
    }

    // 检查某个值是否属于该枚举
    public boolean containsValue(String value) {
        return values.contains(value);
    }

    // 根据字符串值解析为对应的枚举类型
    public static Optional<OrderStatusType> resolve(String value) {
        for (OrderStatusType type : values()) {
            if (type.containsValue(value)) {
                return Optional.of(type);
            }
        }
        return Optional.empty();
    }
}
