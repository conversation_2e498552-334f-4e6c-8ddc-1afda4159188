package com.zsmall.product.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.domain.entity.request.OpenApiInventoryInformationByTenantId;
import com.hengjian.openapi.domain.entity.request.OpenApiProductSkuStock;
import com.hengjian.openapi.domain.entity.request.OpenApiRequestEntity;
import com.hengjian.openapi.domain.entity.request.OpenApiWarehouseInfoRequest;
import com.hengjian.openapi.domain.entity.response.OpenApiWarehouseAdminInfoResponse;
import com.hengjian.openapi.domain.entity.response.OpenApiWarehouseInfoResponse;
import com.hengjian.openapi.service.IOpenApiService;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.dto.price.OpenAPIProductPrice;
import com.zsmall.product.entity.domain.dto.price.OpenApiProductSkuPriceWithSite;
import com.zsmall.product.entity.domain.dto.product.OpenApiProductSkuDetails;
import com.zsmall.product.entity.domain.vo.ProductSkuStockApiVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.mapper.ProductSkuMapper;
import com.zsmall.product.entity.mapper.ProductSkuPriceMapper;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.product.entity.util.ProductCodeGenerator;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.mapper.SiteCountryCurrencyMapper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import com.zsmall.warehouse.entity.mapper.WarehouseAddressMapper;
import com.zsmall.warehouse.entity.mapper.WarehouseMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/open/api")
public class ProductOpenAPiController {
    private final HttpServletRequest request;
    private final IOpenApiService openApiService;
    private final ProductSkuMapper productSkuMapper;
    private final IProductSkuPriceService productSkuPriceService;
    private final IProductService productService;
    private final IProductCategoryService productCategoryService;
    private final IProductSkuAttachmentService productSkuAttachmentService;
    private final IProductSkuDetailService productSkuDetailService;
    private final ProductSkuPriceMapper productSkuPriceMapper;
    private final ISysTenantService sysTenantService;
    private final SiteCountryCurrencyMapper siteCountryCurrencyMapper;
    private final WarehouseMapper warehouseMapper;
    private final WarehouseAddressMapper warehouseAddressMapper;
    private final ProductSkuStockMapper productSkuStockMapper;
    private final ProductCodeGenerator productCodeGenerator;
    private final ISysConfigService sysConfigService;

    /**
     * 获取库存信息API
     */
    @GetMapping("/order/getInventoryInformation")
    public R getInventoryBySkus(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        try {
            //校验请求参数/签名
            openApiService.checkAPISign(openApiRequestEntity);
            List<String> skuList = JSONObject.parseArray(openApiRequestEntity.getParam(), String.class);
            if (skuList.size() > 10) {
                return R.fail("非法的请求! sku数量不能超过10个");
            }
            HashSet<String> skuSet = new HashSet<>(skuList);
            AtomicReference<List<ProductSkuStockApiVo>> productSkuStockVoList = new AtomicReference<>();
            TenantHelper.ignore(() -> {
                productSkuStockVoList.set(productSkuMapper.selectProductSkuStockBySkus(skuSet,null));
            });
            return R.ok(productSkuStockVoList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取商品关联的供货商库存信息
     */
    @GetMapping("/getInventoryInformationByTenantId")
    public R getInventoryBySkusAndTenantId(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        try {
            //校验请求参数/签名
            openApiService.checkAPISign(openApiRequestEntity);
            OpenApiInventoryInformationByTenantId information = JSONObject.parseObject(openApiRequestEntity.getParam(), OpenApiInventoryInformationByTenantId.class);
            List<String> skuList = information.getSkuList();
            if (CollUtil.isEmpty(skuList)) {
                return R.fail("非法的请求! sku列表不能为空");
            }
            if (skuList.size() > 10) {
                return R.fail("非法的请求! sku数量不能超过10个");
            }
            if (StrUtil.isEmpty(information.getSupplierTenantId())){
                return R.fail("非法的请求! 供应商ID不能为空");
            }
            HashSet<String> skuSet = new HashSet<>(skuList);
            AtomicReference<List<ProductSkuStockApiVo>> productSkuStockVoList = new AtomicReference<>();
            TenantHelper.ignore(() -> {
                productSkuStockVoList.set(productSkuMapper.selectProductSkuStockBySkus(skuSet, information.getSupplierTenantId()));
            });
            return R.ok(productSkuStockVoList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询供应商所有商品
     */
    @GetMapping("/getSkuListByPage")
    public R getSkuListByPage(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        //校验请求参数/签名
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        JSONObject jsonObject = JSONObject.parseObject(param);
        Integer current = jsonObject.getInteger("current");
        Integer size = jsonObject.getInteger("size");
        if (size>50){
            return R.fail("非法的请求! 每页最多显示50条数据");
        }
        if (ObjectUtil.isNull(current) || ObjectUtil.isNull(size)){
            return R.fail("非法的请求! 分页参数不能为空");
        }
        AtomicReference<IPage<String>> skuListByPageList= new AtomicReference<>();
        TenantHelper.ignore(() -> {
            skuListByPageList.set(productSkuMapper.selectSkuListByPage(new Page<>(current, size)));
        });
        return R.ok(skuListByPageList);
    }

    /**
     *  分页查询仓库编码下面的商品信息
     * @param openApiRequestEntity
     * @return
     */
    @GetMapping("/getSkuInventoryByPage")
    public R getSkuInventoryByPage(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        JSONObject jsonObject = JSONObject.parseObject(param);
        Integer current = jsonObject.getInteger("current");
        Integer size = jsonObject.getInteger("size");
        if (ObjectUtil.isNull(current) || ObjectUtil.isNull(size)){
            return R.fail("非法的请求! 分页参数不能为空");
        }
        if (size>50){
            return R.fail("非法的请求! 每页最多显示50条数据");
        }
        //校验仓库信息
        JSONArray warehouseCodes = jsonObject.getJSONArray("warehouseCodes");
        if (CollectionUtil.isEmpty(warehouseCodes)){
            return R.fail("仓库不能为空");
        }
        List<String> tenantList = TenantHelper.ignore(() -> JSONUtil.toList(sysConfigService.selectConfigByKey("Get_Sku_Inventory_By_Page"), String.class));
        AtomicReference<IPage<ProductSkuStockApiVo>> skuListByPageList= new AtomicReference<>();
        TenantHelper.ignore(() -> {
            skuListByPageList.set(productSkuMapper.selectSkuInventoryByPage(new Page<>(current, size),warehouseCodes.toJavaList(String.class),tenantList));
        });
        return R.ok(skuListByPageList);
    }

    /**
     * 获取仓库列表
     * @param openApiRequestEntity
     * @return R
     */
    @GetMapping("/getWarehouseCodes")
    public R getWarehouseCodes(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        openApiService.checkAPISign(openApiRequestEntity);
        List<String> warehouses = TenantHelper.ignore(() ->  productSkuMapper.getWarehouseCodes("SJN1857"));
        return R.ok(warehouses);
    }

    /**
     * OpenAPI 获取所有的仓库列表
     * @param openApiRequestEntity
     * @return
     */
    @GetMapping("/getWarehouseAdminInfos")
    public R<List<OpenApiWarehouseAdminInfoResponse>> getWarehouseAdminInfos(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        openApiService.checkAPISign(openApiRequestEntity);
        List<WarehouseAdminInfo> warehouses = TenantHelper.ignore(productSkuMapper::getWarehouseAdminInfos);
        List<OpenApiWarehouseAdminInfoResponse> openApiWarehouseAdminInfoResponses = BeanUtil.copyToList(warehouses, OpenApiWarehouseAdminInfoResponse.class);
        return R.ok(openApiWarehouseAdminInfoResponses);
    }

    /**
     * 获取商品价格
     * @param openApiRequestEntity
     * @return R
     */
    @GetMapping("/getProductSkuPrice")
    public R getProductPrice(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        JSONObject jsonObject = JSONObject.parseObject(param);
        if (ObjectUtil.isNull(jsonObject)){
            return R.fail("非法的请求！非法的请求参数productSkuCodes/current,size不能为空");
        }
        boolean isPage=false;
        long total=0;
        int pageCurrent=0;
        int pageSize=0;
        JSONArray productSkuCodes = jsonObject.getJSONArray("productSkuCodes");
        //查询商品编码
        LambdaQueryWrapper<ProductSku> ps = new LambdaQueryWrapper<>();
        List<ProductSku> productSkus=new ArrayList<>();
        if (ObjectUtil.isNotNull(productSkuCodes)){
            //获取分页参数
            if (CollectionUtil.isNotEmpty(productSkuCodes)&&productSkuCodes.size() > 20){
                return R.fail("非法的请求! sku数量不能超过20个");
            }
            ps.in(ProductSku::getProductSkuCode,productSkuCodes);
            ps.eq(ProductSku::getDelFlag,0);
            productSkus = TenantHelper.ignore(()->productSkuMapper.selectList(ps));
        }else {
            Integer current = jsonObject.getInteger("current");
            Integer size = jsonObject.getInteger("size");
            if (ObjectUtil.isNull(current) || ObjectUtil.isNull(size)){
                return R.fail("非法的请求! 分页参数不能为空");
            }
            if (size>20){
                return R.fail("非法的请求! 分页每页最多20条数据");
            }
            isPage=true;
            Page<ProductSku> productSkuPage =TenantHelper.ignore(()-> productSkuMapper.selectPage(new Page<>(current, size), ps));
            productSkus=productSkuPage.getRecords();
            total=productSkuPage.getTotal();
            pageCurrent=current;
            pageSize=size;
        }
        if (CollectionUtil.isEmpty(productSkus)){
            return R.fail(StrUtil.format("非法的请求!,未获取到正确的商品数据,租户ID:{},请求参数:{}",openApiRequestEntity.getTenantId(),openApiRequestEntity.getParam()));
        }
        //转Map
        Map<String, ProductSku> productSkuMap = productSkus.stream()
                                                           .collect(Collectors.toMap(ProductSku::getProductSkuCode, productSku -> productSku));
        //获取商品编码
        Set<String> productSkuCodesSet = productSkus.stream()
                                                    .map(ProductSku::getProductSkuCode)
                                                    .collect(Collectors.toSet());
        //批量获取商品价格
        LambdaQueryWrapper<ProductSkuPrice> p = new LambdaQueryWrapper<>();
        p.in(ProductSkuPrice::getProductSkuCode,productSkuCodesSet);
        Map<String, ProductSkuPrice> productSkuSitePriceMapByCode = productSkuPriceService.getProductSkuSitePriceMapByCode(productSkuCodesSet);
        Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMapByCode = productSkuPriceService.getRuleLevelProductPriceSitePriceMapByCode(productSkuCodesSet, openApiRequestEntity.getTenantId());

        //获取会员价
        List<OpenAPIProductPrice> openAPIProductPriceList = new ArrayList<>();
        for (Object productSkuCode : productSkuCodesSet) {
            //获取会员价
            ProductSku productSku = productSkuMap.get(productSkuCode);
            if (ObjectUtil.isNull(productSku)){
                continue;
            }

            RuleLevelProductPrice memberPrice = ruleLevelProductPriceSitePriceMapByCode.get(productSkuCode + "-US");
            OpenAPIProductPrice openAPIProductPrice = new OpenAPIProductPrice();
            openAPIProductPrice.setSkuCode(productSku.getSku());
            openAPIProductPrice.setProductSkuCode(productSku.getProductSkuCode());
            if (ObjectUtil.isNotNull(memberPrice)){
                openAPIProductPrice.setPlatformUnitPrice(memberPrice.getPlatformUnitPrice());
                openAPIProductPrice.setPlatformDropShippingPrice(memberPrice.getPlatformDropShippingPrice());
                openAPIProductPrice.setPlatformOperationFee(memberPrice.getPlatformOperationFee());
                openAPIProductPrice.setPlatformFinalDeliveryFee(memberPrice.getPlatformFinalDeliveryFee());
                openAPIProductPrice.setPlatformPickUpPrice(memberPrice.getPlatformPickUpPrice());
                openAPIProductPrice.setUpdateTime(memberPrice.getUpdateTime());
            }else {
                ProductSkuPrice productSkuPrice = productSkuSitePriceMapByCode.get(productSku.getProductSkuCode()+"-US");
                //查询商品价格
                if (ObjectUtil.isNotNull(productSkuPrice)){
                    openAPIProductPrice.setPlatformUnitPrice(productSkuPrice.getPlatformUnitPrice());
                    openAPIProductPrice.setPlatformDropShippingPrice(productSkuPrice.getPlatformDropShippingPrice());
                    openAPIProductPrice.setPlatformOperationFee(productSkuPrice.getPlatformOperationFee());
                    openAPIProductPrice.setPlatformFinalDeliveryFee(productSkuPrice.getPlatformFinalDeliveryFee());
                    openAPIProductPrice.setPlatformPickUpPrice(productSkuPrice.getPlatformPickUpPrice());
                    openAPIProductPrice.setUpdateTime(productSkuPrice.getUpdateTime());
                }
            }
            openAPIProductPriceList.add(openAPIProductPrice);
        }
        if (isPage){
            HashMap<String, Object> map = new HashMap<>();
            map.put("total",total);
            map.put("current",pageCurrent);
            map.put("size",pageSize);
            map.put("openAPIProductPriceList",openAPIProductPriceList);
            return R.ok(map);
        }else {
            return R.ok(openAPIProductPriceList);
        }
    }

    /**
     * 获取商品站点下所有价格
     * @param openApiRequestEntity 请求参数
     * @return R
     */
    @GetMapping("/getProductSkuPriceWithSite")
    public R getProductSkuPriceWithSite(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        JSONObject jsonObject = JSONObject.parseObject(param);
        if (ObjectUtil.isNull(jsonObject)){
            return R.fail("非法的请求！非法的请求参数productSkuCodes/current,size不能为空");
        }
        boolean isPage=false;
        long total=0;
        int pageCurrent=0;
        int pageSize=0;
        JSONArray productSkuCodes = jsonObject.getJSONArray("productSkuCodes");
        //查询商品编码
        LambdaQueryWrapper<ProductSku> ps = new LambdaQueryWrapper<>();
        List<ProductSku> productSkus=new ArrayList<>();
        if (ObjectUtil.isNotNull(productSkuCodes)){
           //获取分页参数
            if (CollectionUtil.isNotEmpty(productSkuCodes)&&productSkuCodes.size() > 20){
                return R.fail("非法的请求! sku数量不能超过20个");
            }
            ps.in(ProductSku::getProductSkuCode,productSkuCodes);
            ps.eq(ProductSku::getDelFlag,0);
            productSkus =TenantHelper.ignore(()->productSkuMapper.selectList(ps));
        }else {
            Integer current = jsonObject.getInteger("current");
            Integer size = jsonObject.getInteger("size");
            if (ObjectUtil.isNull(current) || ObjectUtil.isNull(size)){
                return R.fail("非法的请求! 分页参数不能为空");
            }
            if (size>20){
                return R.fail("非法的请求! 分页每页最多20条数据");
            }
            isPage=true;
            Page<ProductSku> productSkuPage =TenantHelper.ignore(()-> productSkuMapper.selectPage(new Page<>(current, size), ps));
            productSkus=productSkuPage.getRecords();
            total=productSkuPage.getTotal();
            pageCurrent=current;
            pageSize=size;
        }
        if (CollectionUtil.isEmpty(productSkus)){
            return R.fail(StrUtil.format("非法的请求!,未获取到正确的商品数据,租户ID:{},请求参数:{}",openApiRequestEntity.getTenantId(),openApiRequestEntity.getParam()));
        }

        //转Map
        Map<String, ProductSku> productSkuMap = productSkus.stream()
                                                              .collect(Collectors.toMap(ProductSku::getProductSkuCode, productSku -> productSku));
        //获取商品编码
        Set<String> productSkuCodesSet = productSkus.stream()
                                                     .map(ProductSku::getProductSkuCode)
                                                     .collect(Collectors.toSet());
        Map<String, ProductSkuPrice> productSkuSitePriceMapByCode = productSkuPriceService.getProductSkuSitePriceMapByCode(productSkuCodesSet);
        Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMapByCode = productSkuPriceService.getRuleLevelProductPriceSitePriceMapByCode(productSkuCodesSet, openApiRequestEntity.getTenantId());
        List<SiteCountryCurrencyVo> siteCurrencyList = siteCountryCurrencyMapper.getSiteCurrencyList();
        siteCurrencyList = siteCurrencyList.stream()
                     .filter(site -> !"US".equals(site.getCountryCode()))
                     .collect(Collectors.toList());
        //判断当前租户是否开启测算
       // Boolean isApprovedTenant = sysTenantService.getIsApprovedTenant(openApiRequestEntity.getTenantId(), 1);


//        //批量获取商品价格
//        LambdaQueryWrapper<ProductSkuPrice> p = new LambdaQueryWrapper<>();
//        p.in(ProductSkuPrice::getProductSkuCode,productSkuCodesSet);
//        List<ProductSkuPrice> productSkuPrices = productSkuPriceService.getBaseMapper().selectList(p);
//        //转MAP
//        Map<String, ProductSkuPrice> productSkuPriceMap = productSkuPrices.stream()
//                                                                         .collect(Collectors.toMap(ProductSkuPrice::getProductSkuCode, productSkuPrice -> productSkuPrice));
        //获取会员价
        List<OpenApiProductSkuPriceWithSite> openAPIProductPriceList = new ArrayList<>();
        for (String productSkuCode : productSkuCodesSet) {
            ProductSku productSku = productSkuMap.get(productSkuCode);
            if (ObjectUtil.isNull(productSku)){
                continue;
            }
            ArrayList<OpenApiProductSkuPriceWithSite.ProductSkuPriceBySite> productSkuPriceBySites = new ArrayList<>();
            OpenApiProductSkuPriceWithSite openAPIProductPrice = new OpenApiProductSkuPriceWithSite();
            openAPIProductPrice.setProductSkuCode(productSkuCode);
            //处理价格
            siteCurrencyList.forEach(s->{
                RuleLevelProductPrice ruleLevelProductPrice = ruleLevelProductPriceSitePriceMapByCode.get(productSkuCode + "-" + s.getCountryCode());
                if (ObjectUtil.isNotNull(ruleLevelProductPrice)){
                    OpenApiProductSkuPriceWithSite.ProductSkuPriceBySite productSkuPriceBySite=new OpenApiProductSkuPriceWithSite.ProductSkuPriceBySite();
                    productSkuPriceBySite.setPlatformUnitPrice(ruleLevelProductPrice.getPlatformUnitPrice());
                    productSkuPriceBySite.setPlatformDropShippingPrice(ruleLevelProductPrice.getPlatformDropShippingPrice());
                    productSkuPriceBySite.setPlatformOperationFee(ruleLevelProductPrice.getPlatformOperationFee());
                    productSkuPriceBySite.setPlatformFinalDeliveryFee(ruleLevelProductPrice.getPlatformFinalDeliveryFee());
                    productSkuPriceBySite.setPlatformPickUpPrice(ruleLevelProductPrice.getPlatformPickUpPrice());
                    productSkuPriceBySite.setUpdateTime(ruleLevelProductPrice.getUpdateTime());
                    productSkuPriceBySite.setProductSkuCode(productSkuCode);
                    productSkuPriceBySite.setSkuCode(productSku.getSku());
                    productSkuPriceBySite.setCurrency(ruleLevelProductPrice.getCurrency());
                    productSkuPriceBySite.setCountryCode(ruleLevelProductPrice.getCountryCode());
                    productSkuPriceBySite.setCurrencySymbol(ruleLevelProductPrice.getCurrencySymbol());
                    productSkuPriceBySite.setIsMemberPrice(true);
                    productSkuPriceBySites.add(productSkuPriceBySite);
                }else {
                    ProductSkuPrice productSkuPrice = productSkuSitePriceMapByCode.get(productSkuCode+"-"+s.getCountryCode());
                    if (ObjectUtil.isNotNull(productSkuPrice)){
                        OpenApiProductSkuPriceWithSite.ProductSkuPriceBySite productSkuPriceBySite=new OpenApiProductSkuPriceWithSite.ProductSkuPriceBySite();
                        productSkuPriceBySite.setPlatformUnitPrice(productSkuPrice.getPlatformUnitPrice());
                        productSkuPriceBySite.setPlatformDropShippingPrice(productSkuPrice.getPlatformDropShippingPrice());
                        productSkuPriceBySite.setPlatformOperationFee(productSkuPrice.getPlatformOperationFee());
                        productSkuPriceBySite.setPlatformFinalDeliveryFee(productSkuPrice.getPlatformFinalDeliveryFee());
                        productSkuPriceBySite.setPlatformPickUpPrice(productSkuPrice.getPlatformPickUpPrice());
                        productSkuPriceBySite.setUpdateTime(productSkuPrice.getUpdateTime());
                        productSkuPriceBySite.setProductSkuCode(productSkuCode);
                        productSkuPriceBySite.setSkuCode(productSku.getSku());
                        productSkuPriceBySite.setCurrency(productSkuPrice.getCurrency());
                        productSkuPriceBySite.setCountryCode(productSkuPrice.getCountryCode());
                        productSkuPriceBySite.setCurrencySymbol(productSkuPrice.getCurrencySymbol());
                        productSkuPriceBySite.setIsMemberPrice(false);
                        productSkuPriceBySites.add(productSkuPriceBySite);
                    }
                }
            });
            openAPIProductPrice.setProductSkuPriceBySites(productSkuPriceBySites);
            openAPIProductPriceList.add(openAPIProductPrice);
        }
        if (isPage){
            HashMap<String, Object> map = new HashMap<>();
            map.put("total",total);
            map.put("current",pageCurrent);
            map.put("size",pageSize);
            map.put("openAPIProductPriceList",openAPIProductPriceList);
            return R.ok(map);
        }else {
            return R.ok(openAPIProductPriceList);
        }
    }


  /**
   * 获取商品信息
   * @param openApiRequestEntity 请求实体
   * @return R
   */
  @GetMapping("/getProductSkuDetails")
  public R<OpenApiProductSkuDetails> getProductSkuDetails(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
    openApiRequestEntity.setUrl(request.getServletPath());
    openApiService.checkAPISign(openApiRequestEntity);
    String param = openApiRequestEntity.getParam();
    JSONObject jsonObject = JSONObject.parseObject(param);
    if (ObjectUtil.isNull(jsonObject)) {
      return R.fail("非法的请求！非法的请求参数ProductSkuCode不能为空");
    }
    String productSkuCode = jsonObject.getString("productSkuCode");
    if (StrUtil.isEmpty(productSkuCode)) {
      return R.fail("非法的请求！非法的请求参数ProductSkuCode不能为空");
    }
    // 查询商品信息
      OpenApiProductSkuDetails apiProductDetails = new OpenApiProductSkuDetails();
    LambdaQueryWrapper<ProductSku> q = new LambdaQueryWrapper<>();
    q.eq(ProductSku::getProductSkuCode, productSkuCode);
    q.eq(ProductSku::getDelFlag, 0);
    ProductSku productSku =TenantHelper.ignore(()->productSkuMapper.selectOne(q)) ;
    if (ObjectUtil.isNull(productSku)) {
      return R.fail("商品不存在:" + productSkuCode);
    }
    apiProductDetails.setProductSkuCode(productSku.getProductSkuCode());
    apiProductDetails.setProductSkuName(productSku.getName());
    apiProductDetails.setSpecComposeName(productSku.getSpecComposeName());
    apiProductDetails.setSpecValName(productSku.getSpecValName());
    apiProductDetails.setVerifyState(productSku.getVerifyState());
    apiProductDetails.setShelfState(productSku.getShelfState());
    // 查询产品信息
    Product product =TenantHelper.ignore(()-> productService.queryByProductSkuCode(productSkuCode));
    if (ObjectUtil.isNull(product)) {
      return R.fail("产品信息不存在:" + productSkuCode);
    }
    apiProductDetails.setSupportedLogistics(product.getSupportedLogistics().name());
    // 查询分类信息
    List<ProductCategory> productCategories =
        TenantHelper.ignore(()->productCategoryService.queryCategoryChainById(product.getBelongCategoryId()))  ;
    List<OpenApiProductSkuDetails.ProductCategoryOpenApi> productCategoryOpenApis =
        BeanUtil.copyToList(productCategories, OpenApiProductSkuDetails.ProductCategoryOpenApi.class);
    apiProductDetails.setProductCategoryOpenApiList(productCategoryOpenApis);
    // 查询商品图片
    LambdaQueryWrapper<ProductSkuAttachment> q1 = new LambdaQueryWrapper<>();
    q1.eq(ProductSkuAttachment::getProductSkuId, productSku.getId());
    q1.eq(ProductSkuAttachment::getDelFlag, 0);
    List<ProductSkuAttachment> productSkuAttachments =
        TenantHelper.ignore(()->productSkuAttachmentService.getBaseMapper().selectList(q1)) ;
    List<OpenApiProductSkuDetails.ProductSkuAttachmentOpenApi> productCategoryOpenApis1 =
        BeanUtil.copyToList(
            productSkuAttachments, OpenApiProductSkuDetails.ProductSkuAttachmentOpenApi.class);
    apiProductDetails.setProductSkuAttachmentOpenApiList(productCategoryOpenApis1);
    // 查询商品详情

    ProductSkuDetail productSkuDetail =
        TenantHelper.ignore(()->productSkuDetailService.queryByProductSkuCode(productSkuCode)) ;
      OpenApiProductSkuDetails.ProductSkuDetailOpenApi productSkuDetailOpenApi =
        BeanUtil.copyProperties(
            productSkuDetail, OpenApiProductSkuDetails.ProductSkuDetailOpenApi.class);
    apiProductDetails.setProductSkuDetailOpenApi(productSkuDetailOpenApi);
    return R.ok(apiProductDetails);
  }

    /**
     * 根据仓库系统编码/仓库编码获取仓库信息
     * @param openApiRequestEntity
     * @return
     */
    @GetMapping("/getWarehouseInfo")
    public R<OpenApiWarehouseInfoResponse> getWarehouseInfo(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        try {
            openApiService.checkAPISign(openApiRequestEntity);
            OpenApiWarehouseInfoRequest information = JSONObject.parseObject(openApiRequestEntity.getParam(), OpenApiWarehouseInfoRequest.class);
            if (StrUtil.isEmpty(information.getWarehouseSystemCode())) {
                return R.fail("非法的请求! 仓库系统编码不能为空");
            }
            if (StrUtil.isEmpty(information.getSupplierTenantId())){
                return R.fail("非法的请求! 供应商ID不能为空");
            }
            LambdaQueryWrapper<Warehouse> q = new LambdaQueryWrapper<>();
            q.eq(Warehouse::getWarehouseSystemCode, information.getWarehouseSystemCode());
            q.eq(Warehouse::getWarehouseState,1);
            q.eq(Warehouse::getTenantId, information.getSupplierTenantId());
            q.eq(Warehouse::getDelFlag, 0);
            Warehouse warehouse =TenantHelper.ignore(()->warehouseMapper.selectOne(q));
            OpenApiWarehouseInfoResponse openApiWarehouseInfoResponse = BeanUtil.copyProperties(warehouse, OpenApiWarehouseInfoResponse.class);
            if (ObjectUtil.isNotNull(warehouse)){
                LambdaQueryWrapper<WarehouseAddress> qs = new LambdaQueryWrapper<>();
                qs.eq(WarehouseAddress::getWarehouseSystemCode, warehouse.getWarehouseSystemCode());
                qs.eq(WarehouseAddress::getDelFlag,0);
                WarehouseAddress warehouseAddress = warehouseAddressMapper.selectOne(qs);
                BeanUtil.copyProperties(warehouseAddress,openApiWarehouseInfoResponse);
            }
            return R.ok(openApiWarehouseInfoResponse);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 供应商更新库存接口
     * @param openApiRequestEntity 请求实体
     * @return R
     */
    @Transactional
    @PostMapping("/updateProductSkuStock")
    public R updateProductSkuStock(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        try {
            openApiService.checkAPISign(openApiRequestEntity);
            SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(openApiRequestEntity.getTenantId());
            if (ObjectUtil.notEqual(sysTenantVo.getTenantType(), TenantType.Supplier.name())){
                return R.fail("非法的请求! 更新库存操作只能由供应商操作");
            }
            List<OpenApiProductSkuStock> productSkuStocks = JSONObject.parseArray(openApiRequestEntity.getParam(), OpenApiProductSkuStock.class);
            if (productSkuStocks.size()>50){
                return R.fail("非法的请求! 更新库存操作只能一次更新50个商品");
            }
            Set<String> productSkuCodeList = productSkuStocks.stream()
                                                          .map(OpenApiProductSkuStock::getProductSkuCode)
                                                          .collect(Collectors.toSet());

            Set<String> warehouseSystemCodeList = productSkuStocks.stream()
                                                              .map(OpenApiProductSkuStock::getWarehouseSystemCode)
                                                              .collect(Collectors.toSet());
            LambdaQueryWrapper<ProductSku> q = new LambdaQueryWrapper<>();
            q.in(ProductSku::getProductSkuCode,productSkuCodeList);
            q.eq(ProductSku::getTenantId,openApiRequestEntity.getTenantId());
            List<ProductSku> productSkus =TenantHelper.ignore(()->productSkuMapper.selectList(q));
            Map<String, ProductSku> productSkuMap = productSkus.stream()
                                                                  .collect(Collectors.toMap(ProductSku::getProductSkuCode, productSku -> productSku));

            LambdaQueryWrapper<Warehouse> w = new LambdaQueryWrapper<>();
            w.in(Warehouse::getWarehouseSystemCode,warehouseSystemCodeList);
            w.eq(Warehouse::getTenantId,openApiRequestEntity.getTenantId());
            List<Warehouse> warehouses =TenantHelper.ignore(()->warehouseMapper.selectList(w));
            Map<String, Warehouse> warehousesMap = warehouses.stream()
                                                               .collect(Collectors.toMap(Warehouse::getWarehouseSystemCode, warehouse -> warehouse));

          productSkuStocks.forEach(
              s -> {
                ProductSku productSku = productSkuMap.get(s.getProductSkuCode());
                if (ObjectUtil.isNull(productSku)) {
                  throw new RuntimeException("非法的请求! 商品不存在:" + s.getProductSkuCode());
                }
                Warehouse warehouse = warehousesMap.get(s.getWarehouseSystemCode());
                if (ObjectUtil.isNull(warehouse)) {
                  throw new RuntimeException("非法的请求! 仓库不存在:" + s.getWarehouseSystemCode());
                }
                if (warehouse.getWarehouseState() != 1) {
                  throw new RuntimeException("非法的请求! 仓库已停用:" + s.getWarehouseSystemCode());
                }
                if (ObjectUtil.isNull(s.getStockAvailable())) {
                  throw new RuntimeException("非法的请求! 自提库存数量不能为空:" + s.getProductSkuCode());
                }
                if (ObjectUtil.isNull(s.getDropShippingStockAvailable())) {
                  throw new RuntimeException("非法的请求! 代发库存标识不能为空:" + s.getProductSkuCode());
                }
                if (s.getDropShippingStockAvailable() != 0 && s.getDropShippingStockAvailable() != 1) {
                  throw new RuntimeException(
                      "非法的请求! 代发库存标识 0单仓,1非单仓(等于自提库存)错误:" + s.getDropShippingStockAvailable());
                }
                LambdaQueryWrapper<ProductSkuStock> wa = new LambdaQueryWrapper<>();
                wa.eq(ProductSkuStock::getProductSkuCode, s.getProductSkuCode());
                wa.eq(ProductSkuStock::getWarehouseSystemCode, s.getWarehouseSystemCode());
                wa.eq(ProductSkuStock::getTenantId, openApiRequestEntity.getTenantId());
                ProductSkuStock productSkuStock =
                    TenantHelper.ignore(() -> productSkuStockMapper.selectOne(wa));
                if (ObjectUtil.isNotNull(productSkuStock)) {
                  productSkuStock.setStockTotal(s.getStockAvailable());
                  productSkuStock.setStockAvailable(s.getStockAvailable());
                  productSkuStock.setDropShippingStockAvailable(s.getDropShippingStockAvailable());
                  productSkuStock.setUpdateTime(new Date());
                  TenantHelper.ignore(()->productSkuStockMapper.updateById(productSkuStock));
                  log.info(
                        StrUtil.format(
                            "[OpenApi更新库存接口],租户:{},开始更新库存最新自提库存:{},最新代发库存标识:{},商品编码:{}，仓库编码:{},更新时间{}",
                            openApiRequestEntity.getTenantId(),
                            s.getStockAvailable(),
                            s.getDropShippingStockAvailable(),
                            s.getProductSkuCode(),
                            s.getWarehouseSystemCode(),
                            new Date()));
                } else {
                  ProductSkuStock productSkuStockInsert = new ProductSkuStock();
                  productSkuStockInsert.setId(IdUtil.getSnowflakeNextId());
                  productSkuStockInsert.setTenantId(openApiRequestEntity.getTenantId());
                  String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);
                  productSkuStockInsert.setStockCode(stockCode);
                  productSkuStockInsert.setStockTotal(s.getStockAvailable());
                  productSkuStockInsert.setStockReserved(0);
                  productSkuStockInsert.setStockAvailable(s.getStockAvailable());
                  productSkuStockInsert.setStockState(GlobalStateEnum.Valid);
                  productSkuStockInsert.setErpSku(productSku.getErpSku());
                  productSkuStockInsert.setProductCode(productSku.getProductCode());
                  productSkuStockInsert.setProductSkuCode(productSku.getProductSkuCode());
                  productSkuStockInsert.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                  productSkuStockInsert.setDropShippingStockAvailable(s.getDropShippingStockAvailable());
                  TenantHelper.ignore(()->productSkuStockMapper.insert(productSkuStockInsert));
                  log.info(
                      StrUtil.format(
                            "[OpenApi更新库存接口],租户:{},开始插入库存最新自提库存:{},最新代发库存标识:{},商品编码:{}，仓库编码:{},时间{}",
                            openApiRequestEntity.getTenantId(),
                            s.getStockAvailable(),
                            s.getDropShippingStockAvailable(),
                            s.getProductSkuCode(),
                            s.getWarehouseSystemCode(),
                            new Date()));
                }
              });
                return R.ok("操作成功");
            } catch (Exception e) {
                return R.fail(e.getMessage());
            }
    }

}
