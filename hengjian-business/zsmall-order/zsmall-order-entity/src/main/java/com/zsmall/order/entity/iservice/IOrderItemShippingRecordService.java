package com.zsmall.order.entity.iservice;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.bo.OrderItemShippingRecordBo;
import com.zsmall.order.entity.mapper.OrderItemShippingRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 子订单出货单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class IOrderItemShippingRecordService extends ServiceImpl<OrderItemShippingRecordMapper, OrderItemShippingRecord> {

    public boolean saveNotTenant(OrderItemShippingRecord entity) {
        return TenantHelper.ignore(() -> super.save(entity));
    }

    /**
     * 根据出货单编号查询（无视租户）
     * @param shippingNo
     * @return
     */
    public OrderItemShippingRecord queryByShippingNoNotTenant(String shippingNo) {
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItemShippingRecord::getShippingNo, shippingNo);
        return TenantHelper.ignore(() -> super.getOne(lqw));
    }

    /**
     * 根据出货单编号批量查询（无视租户）
     * @param shippingNoList
     * @return
     */
    public List<OrderItemShippingRecord> queryByShippingNoNotTenant(List<String> shippingNoList) {
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderItemShippingRecord::getShippingNo, shippingNoList);
        return TenantHelper.ignore(() -> super.list(lqw));
    }

    /**
     * 根据订单号批量查询
     *
     * @param orderNoList
     * @return
     */
    public List<OrderItemShippingRecord> queryListByOrderNoList(List<String> orderNoList){
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderItemShippingRecord::getOrderNo, orderNoList);
        return TenantHelper.ignore(() -> super.list(lqw));
    }


    public boolean existsShippingNo(String shippingNo) {
        return baseMapper.existsShippingNo(shippingNo);
    }

    private LambdaQueryWrapper<OrderItemShippingRecord> buildQueryWrapper(OrderItemShippingRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierTenantId()), OrderItemShippingRecord::getSupplierTenantId, bo.getSupplierTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderItemShippingRecord::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderItemShippingRecord::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), OrderItemShippingRecord::getChannelType, bo.getChannelType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseType()), OrderItemShippingRecord::getWarehouseType, bo.getWarehouseType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), OrderItemShippingRecord::getWarehouseCode, bo.getWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), OrderItemShippingRecord::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingNo()), OrderItemShippingRecord::getShippingNo, bo.getShippingNo());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingState()), OrderItemShippingRecord::getShippingState, bo.getShippingState());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingErrorCode()), OrderItemShippingRecord::getShippingErrorCode, bo.getShippingErrorCode());
        lqw.eq(bo.getSystemManaged() != null, OrderItemShippingRecord::getSystemManaged, bo.getSystemManaged());
        return lqw;
    }

    /**
     * 根据子订单号更新发货单的状态
     * @param orderItemNo
     * @param shippingStateEnum
     * @return
     */
    public Boolean updateShippingStateByOrderItem(String orderItemNo, ShippingStateEnum shippingStateEnum) {
        LambdaUpdateWrapper<OrderItemShippingRecord> luw = Wrappers.lambdaUpdate();
        luw.set(OrderItemShippingRecord::getShippingState, shippingStateEnum);
        luw.set(OrderItemShippingRecord::getSystemManaged, false);
        luw.eq(OrderItemShippingRecord::getOrderItemNo, orderItemNo);
        return TenantHelper.ignore(() -> baseMapper.update(null, luw)) > 0;
    }

    /**
     *
     * @param orderNo
     * @param stockManager
     * @return
     */
    public List<OrderItemShippingRecord> getListByOrderNoAndType(String orderNo, StockManagerEnum stockManager) {
        return TenantHelper.ignore(() -> baseMapper.getListByOrderNoAndType(orderNo, stockManager.name()));
    }

    @InMethodLog("根据出货单是否由系统托管轮询搜索出货单")
    public List<OrderItemShippingRecord> getListBySystemManaged(WarehouseTypeEnum warehouseType, List<ShippingStateEnum> shippingStateList, Boolean systemManaged) {
        return baseMapper.getListBySystemManaged(warehouseType.name(), shippingStateList, systemManaged);
    }

    public List<OrderItemShippingRecord> queryByOrderExtendId(String orderExtendId) {
        return baseMapper.queryByOrderExtendId(orderExtendId);
    }
    public List<OrderItemShippingRecord> queryListByOrderExtendId(List<String> orderExtendIds) {
        return baseMapper.queryListByOrderExtendId(orderExtendIds);
    }
    /**
     * 功能描述：按顺序查询,每个订单号保留时间最新的记录
     *
     * @param exceptionOrderSns 例外顺序sns
     * <AUTHOR>
     * @date 2025/07/09
     */
    public Map<String, JSONObject> queryMapByOrderNoList(List<String> exceptionOrderSns) {
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderItemShippingRecord::getOrderNo, exceptionOrderSns);
        List<OrderItemShippingRecord> shippingRecords = TenantHelper.ignore(() -> list(lqw));
        List<OrderItemShippingRecord> distinctRecords = new ArrayList<>(
            shippingRecords.stream()
                           // 新增过滤条件：只保留 shipping_error_code 不为 null 的记录
                           .filter(record -> record.getShippingErrorCode() != null)
                           .collect(Collectors.toMap(
                               OrderItemShippingRecord::getOrderNo,
                               Function.identity(),  // 等价于 record -> record
                               (oldRecord, newRecord) -> {
                                   // 安全的时间比较（处理 null 情况）
                                   Date newTime = newRecord.getUpdateTime();
                                   Date oldTime = oldRecord.getUpdateTime();

                                   // 处理两个时间均为 null 的情况
                                   if (newTime == null && oldTime == null) {
                                       return newRecord; // 或 oldRecord（根据业务需求）
                                   }
                                   // 处理 newTime 为 null 的情况
                                   if (newTime == null) {
                                       return oldRecord;
                                   }
                                   // 处理 oldTime 为 null 的情况
                                   if (oldTime == null) {
                                       return newRecord;
                                   }
                                   // 正常比较时间
                                   return newTime.after(oldTime) ? newRecord : oldRecord;
                               }
                           ))
                           .values()
        );
        // 理论上只要是abnormal的订单都会有异常信息,这里做一个防御机制
        Map<String, String> map = distinctRecords.stream()
                                                 .filter(Objects::nonNull)
                                                 .filter(record -> record.getOrderNo() != null)
                                                 .collect(Collectors.toMap(
                                                     OrderItemShippingRecord::getOrderNo,
                                                     record -> Optional.ofNullable(record.getShippingErrorCode())
                                                                       .orElse("N/A"),
                                                     (existingValue, newValue) -> existingValue
                                                 ));
        return replaceErrorValues(map);
    }

    /**
     * 功能描述：替换错误值
     *
     * @param map 地图
     * <AUTHOR>
     * @date 2025/07/10
     */
    public Map<String,JSONObject> replaceErrorValues(Map<String, String> map) {
        Map<String, JSONObject> map1 = new HashMap();
        final JSONObject skuMessage = ZSMallStatusCodeEnum.ABNORMAL_SKU_INVENTORY_IS_INSUFFICIENT.toJSON();
        final JSONObject shippingMessage = ZSMallStatusCodeEnum.ABNORMAL_CREATED_FOR_THE_SHIPPING_ORDER.toJSON();

        map.forEach((key, value) -> {
            if (value != null && value.contains("SKU库存不足")) {
                map1.put(key, skuMessage);
            }else {
                map1.put(key, shippingMessage);
            }
        });
        return map1;
    }
}
