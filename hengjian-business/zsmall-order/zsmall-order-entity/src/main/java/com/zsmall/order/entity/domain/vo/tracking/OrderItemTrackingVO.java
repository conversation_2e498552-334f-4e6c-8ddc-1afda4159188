package com.zsmall.order.entity.domain.vo.tracking;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.zsmall.common.domain.dto.ExcelBaseDTO;
import lombok.Data;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/25 14:30
 */
@Data
@ExcelIgnoreUnannotated
public class OrderItemTrackingVO extends ExcelBaseDTO {

    /**
     * 主订单编号
     */
    @ExcelProperty(value = "*订单编号(分销系统订单编号)")
    private String orderNo;

    @ExcelProperty(value = "渠道订单号")
    private String channelOrderNo;

    @ExcelProperty(value = "*Tracking")
    private String trackingNo;

    @ExcelProperty(value = "*发货方式(分销系统发货方式)格式:UPS,FedEx,AMSP,OnTrac")
    private String carrier;

    @ExcelProperty(value = "*SKU ID(分销系统中的SKU ID)")
    private String productSkuCode;

    @ExcelProperty(value = "*发货数量(包裹中SKU ID的发货数量)")
    private int quantity;

    @ExcelProperty(value = "发货时间(默认当前时间(GMT时间),格式:2020/1/1 00:00:00)")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private String deliveryTime;

    /**
     * excel行号
     */
    private int excelRow;

}

