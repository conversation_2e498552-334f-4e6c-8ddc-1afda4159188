package com.zsmall.order.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/7/11 10:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplierRefundHandleEvent {


    private String tenantId;
    /**
     * 订单审核意见 0:同意 1:拒绝
     */

    private Integer orderReviewOpinion;

    /**
     * 行id
     */
    private String orderNo;
}
