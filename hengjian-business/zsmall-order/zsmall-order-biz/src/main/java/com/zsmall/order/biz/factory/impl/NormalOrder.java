package com.zsmall.order.biz.factory.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.domain.bo.TrackingNoBo;
import com.zsmall.common.domain.vo.IntactAddressInfoVo;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderRefund.RefundAmountStateEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.order.biz.factory.OrderHandleInterface;
import com.zsmall.order.biz.service.OrderRefundRuleService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.vo.OrderDetailsAttachmentVo;
import com.zsmall.order.entity.domain.vo.order.*;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.salesChannel.TenantSalesChannelVo;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 普通订单实现类
 *
 * <AUTHOR>
 * @date 2023/2/20
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class NormalOrder implements OrderHandleInterface {

    public static final String dateFormat = "yyyy-MM-dd HH:mm:ss";

    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final BusinessParameterService businessParameterService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final OrderRefundRuleService orderRefundRuleService;
    private final IOrderRefundItemService iOrderRefundItemService;
    private final IOrderRefundService iOrderRefundService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final OrderSupport orderSupport;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
//    private final IProductActivityItemService iProductActivityItemService;
    private final IOrdersService iOrdersService;
    private final IWarehouseService warehouseService;
    private final ISysTenantService sysTenantService;

    /**
     * 构建订单Body
     *
     * @param orders
     * @return
     */
    @Override
    public OrderPageVo buildOrderBody(Orders orders, String activityType) {
        OrderType orderType = orders.getOrderType();
        String tenantId = LoginHelper.getTenantId();
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        if (ObjectUtil.isNull(tenantType)){
            SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(orders.getTenantId());
            if (ObjectUtil.isNotNull(sysTenantVo)){
                tenantType=TenantType.valueOf(sysTenantVo.getTenantType());
            }
        }
        OrderPageVo orderPageVo = new OrderPageVo();
        orderPageVo.setActivityType(orders.getActivityType());
        orderPageVo.setActivityCode(orders.getActivityCode());
        orderPageVo.setCountryCode(orders.getCountryCode());
        orderPageVo.setOrderExtendId(orders.getOrderExtendId());
        orderPageVo.setCurrency(orders.getCurrency());
        orderPageVo.setCurrencySymbol(orders.getCurrencySymbol());
        orderPageVo.setTrackingFlag(orders.getTrackingFlag());
        orderPageVo.setExceptionCode(orders.getExceptionCode());
        orderPageVo.setOrderSource(orders.getOrderSource());
        orderPageVo.setIsShow(orders.getIsShow());
        orderPageVo.setChannelId(orders.getChannelId());
        orderPageVo.setLatestShipDate(orders.getLatestShipDate());
        orderPageVo.setPayDate(DateUtil.formatDateTime(orders.getPayTime()));
        orderPageVo.setTotalQuantity(orders.getTotalQuantity());
        orderPageVo.setWarehouseExpectedShipDate(orders.getWarehouseExpectedShipDate());
        orderPageVo.setCancelStatus(orders.getCancelStatus());
        String userName = "";
        //1.0.2 改进为都能看到
        if (ObjectUtil.equals(TenantType.Manager, tenantType)) {
            String orderTenantId = orders.getTenantId();
            orderPageVo.setDistributorId(orderTenantId);
            orderPageVo.setCreateBy(orders.getCreateBy());
        }

        // 收益
        BigDecimal margin = BigDecimal.ZERO;

        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orders.getId());
        List<OrderItemVo> orderItemBodies = new ArrayList<>();
        Boolean canConfirmReceiptOrder = false;
        StringBuilder WMSFailMessage = new StringBuilder();
        boolean isRecreateWMSOrder = false;
        List<String> supplierCodes = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OrderItem orderItem : orderItems) {
                String orderItemNo = orderItem.getOrderItemNo();
                String activityCode = orderItem.getActivityCode();
                BigDecimal saleTotalPrice = orderItem.getChannelSaleTotalAmount();
                BigDecimal totalPrice = orderItem.getPlatformPayableTotalAmount();

                if (saleTotalPrice != null && !NumberUtil.equals(saleTotalPrice, BigDecimal.ZERO)) {
                    BigDecimal itemMargin = NumberUtil.sub(saleTotalPrice, totalPrice);
                    log.info("orderItemNo = {} itemMargin = {}", orderItemNo, itemMargin);
                    margin = NumberUtil.add(margin, itemMargin);
                    log.info("margin = {}", margin);
                }

                String supplierTenantId = orderItem.getSupplierTenantId();
                if (StrUtil.isNotBlank(supplierTenantId)) {
                    supplierCodes.add(supplierTenantId);
                }
                OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                if (Objects.equals(tenantType, TenantType.Supplier) && !StringUtils.equals(supplierTenantId, tenantId)) {
                    continue;
                }

                OrderItemVo orderItemBody = new OrderItemVo();
               // BigDecimal platformActualTotalAmount = orderItem.getPlatformActualTotalAmount();
             //   BigDecimal originalActualTotalAmount = orderItem.getOriginalActualTotalAmount();
                BigDecimal platformActualTotalAmount = orders.getPlatformActualTotalAmount();
                BigDecimal originalActualTotalAmount = orders.getOriginalActualTotalAmount();
                Integer totalQuantity = orderItem.getTotalQuantity();
                OrderStateType orderItemStatus = orderItem.getOrderState();
                LogisticsProgress fulfillment = orderItem.getFulfillmentProgress();
                log.info("发货时间：{}", orderItem.getDispatchedTime());
                orderItemBody.setDeliveryTime(DateUtil.format(orderItem.getDispatchedTime(), dateFormat));
                orderItemBody.setChannelSku(orderItem.getChannelSku());
                if (ObjectUtil.isNotNull(orderProductSku)) {
                    orderItemBody.setItemNo(orderProductSku.getProductSkuCode());
                    orderItemBody.setSku(orderProductSku.getSku());
                    orderItemBody.setImageShowUrl(orderProductSku.getImageShowUrl());
                    orderItemBody.setProductName(orderProductSku.getProductName());
                    orderItemBody.setActivityType(orderProductSku.getActivityType());
                    orderItemBody.setProductCode(orderProductSku.getProductCode());

                    //履约错误信息设置 TODO
                    /*if (ObjectUtil.notEqual(tenantType, TenantType.Distributor)
                        && ObjectUtil.equals(orderItem.getFulfillmentProgress(), LogisticsProgress.UnDispatched)) {
                        isRecreateWMSOrder = true;
                        WMSFailMessage.append("Item No. [").append(orderProductSku.getSupplierProductCode())
                            .append("] BiaArk sales order create failed, reason: ").append(orderItem.getBizarkFailMessage());
                    } else if (ObjectUtil.notEqual(orderItem.getBizarkStatus(), BizarkStatusType.CreateFailed)
                        && StringUtils.isNotBlank(orderItem.getFulfillmentMessage())
                        && ObjectUtil.notEqual(tenantType, TenantType.Supplier)) {
                        WMSFailMessage.append("Item No. [").append(orderProductSku.getSupplierProductCode())
                            .append("] Fulfillment error, reason: ").append(orderItem.getFulfillmentMessage());
                    } else if (StringUtils.isNotBlank(orderItem.getFulfillmentMessage()) && ObjectUtil.equals(tenantType, TenantType.Manager)) {
                        WMSFailMessage.append("Item No. [").append(orderProductSku.getSupplierProductCode())
                            .append("] Fulfillment error, reason: ").append(orderItem.getFulfillmentMessage());
                    }*/

                    if (StrUtil.isNotBlank(activityCode)) {
                        if (ObjectUtil.equals(tenantType, TenantType.Distributor)) {
                            orderItemBody.setActivityCode(activityCode);
                        } else {
//                           ProductActivityItem productActivity = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityCode).build());
//                           orderItemBody.setActivityCode(productActivity.getActivityCodeParent());
                        }
                    }
                }

                if (TenantType.Manager.equals(tenantType) && CollUtil.isNotEmpty(supplierCodes)) {
                    String supplierIds = String.join(",", supplierCodes);
                    orderPageVo.setSupplierIds(supplierIds);
                }
                //子订单确认收货按钮
                Boolean canConfirmReceipt = ObjectUtil.equals(orderItemStatus, OrderStateType.Paid) && ObjectUtil.equals(fulfillment, LogisticsProgress.Dispatched);
                if (!canConfirmReceiptOrder && canConfirmReceipt) { //订单确认收货按钮
                    canConfirmReceiptOrder = true;
                }
                orderItemBody.setCanConfirmReceipt(canConfirmReceipt);
                orderItemBody.setOrderItemNo(orderItemNo);
                orderItemBody.setNum(totalQuantity);
                if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
                    //活动订金单价（供应商）
                    BigDecimal depositUnitPriceSup = BigDecimal.ZERO;
                    BigDecimal depositTotalPriceSup = NumberUtil.mul(depositUnitPriceSup, totalQuantity);
                    BigDecimal productTotalPriceSup = NumberUtil.add(originalActualTotalAmount, depositTotalPriceSup);
                    orderItemBody.setProductTotalPrice(productTotalPriceSup);
                    totalAmount = NumberUtil.add(totalAmount, productTotalPriceSup);
                } else {
                    //活动订金单价
                    BigDecimal depositUnitPrice = BigDecimal.ZERO;
                    BigDecimal depositTotalPrice = NumberUtil.mul(depositUnitPrice, totalQuantity);
                    // 拿产品总价
                    BigDecimal productTotalPrice = BigDecimal.ZERO;
                    if(ChannelTypeEnum.TikTok.equals(orders.getChannelType())){
                        if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                            productTotalPrice = orders.getOriginalTotalDropShippingPrice();
                        }
                        if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                            productTotalPrice = orders.getOriginalTotalPickUpPrice();

                        }
                    }else{
                        if(ObjectUtil.isNotEmpty(platformActualTotalAmount)){
                            productTotalPrice = NumberUtil.add(platformActualTotalAmount, depositTotalPrice);
                        }else {
                            productTotalPrice = null;
                        }

                    }
                    orderItemBody.setProductTotalPrice(productTotalPrice);

//                    totalAmount = NumberUtil.add(totalAmount, productTotalPrice);

                    if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                        totalAmount = orders.getOriginalTotalDropShippingPrice();
                    }
                    if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                        totalAmount = orders.getOriginalTotalPickUpPrice();
                    }

                }
                OrderProductSkuBo orderProductSkuBody = new OrderProductSkuBo();
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                if (ObjectUtil.isNotNull(orderItemProductSku)){
                    orderProductSkuBody.setSku(orderItemProductSku.getSku());
                    orderProductSkuBody.setItemNo(orderItemProductSku.getProductSkuCode());
                    orderProductSkuBody.setWarehouseSystemCode(orderItemProductSku.getWarehouseSystemCode());
                    orderProductSkuBody.setImgUrl(orderItemProductSku.getImageShowUrl());
                    if (StrUtil.isNotEmpty(orderProductSkuBody.getWarehouseSystemCode())){
                        Warehouse warehouse = warehouseService.queryByWarehouseSystemCode(orderItemProductSku.getWarehouseSystemCode());
                        if (ObjectUtil.isNotNull(warehouse)){
                            orderProductSkuBody.setWarehouseCode(warehouse.getWarehouseCode());
                            orderProductSkuBody.setWarehouseName(warehouse.getWarehouseName());
                        }
                    }
                }
                orderProductSkuBody.setSalesChannelOrderId(orderItemNo);
                orderItemBody.setProductSku(orderProductSkuBody);
                orderItemBody.setDeliveryTime(DateUtil.formatDateTime(orderItem.getDispatchedTime()));
                orderItemBodies.add(orderItemBody);
            }
        }
        orderPageVo.setMargin(DecimalUtil.bigDecimalToString(margin));

        if (CollUtil.isNotEmpty(orderItemBodies)) {
            orderPageVo.setOrderItems(orderItemBodies);
        }
        orderPageVo.setCanConfirmReceipt(canConfirmReceiptOrder);
        orderPageVo.setOrderId(orders.getOrderNo());
        orderPageVo.setChannelOrderId(orders.getChannelOrderName());

        if (orderType != null) {
            orderPageVo.setOrderType(orderType.name());
        }
        orderPageVo.setChannelAlias(orders.getChannelAlias());

        // 供应商价格直接拿商品价格
        if (!Objects.equals(tenantType, TenantType.Supplier)) {
            if(ChannelTypeEnum.TikTok.equals(orders.getChannelType())||ChannelTypeEnum.Temu.equals(orders.getChannelType())||ChannelTypeEnum.Open.equals(orders.getChannelType())){
                orderPageVo.setTotalNumber(totalAmount);
            }else {
                orderPageVo.setTotalNumber(orders.getPlatformActualTotalAmount());
            }
        }
        if(ObjectUtil.isNotEmpty(totalAmount)){
            orderPageVo.setTotal(DecimalUtil.bigDecimalToString(totalAmount));
        }else {
            orderPageVo.setTotal(null);
        }
        orderPageVo.setCustomer(userName);

        orderPageVo.setItem(orders.getTotalQuantity());

        if (ObjectUtil.isNotEmpty(orders.getChannelId())){
            TenantSalesChannel channel = TenantHelper.ignore(() -> iTenantSalesChannelService.getBaseMapper()
                                                                                             .selectById(orders.getChannelId()));
            if (ObjectUtil.isNotEmpty(channel)){
                orderPageVo.setSalesChannel(channel.getChannelType());
            }
        }else {
            if (ObjectUtil.isNotNull(orders.getChannelType())) {
                orderPageVo.setSalesChannel(orders.getChannelType().name());
            }
        }
        orderPageVo.setStartTime(DateUtil.format(orders.getCreateTime(), dateFormat));
        orderPageVo.setOrderChannelTime(DateUtil.format(orders.getChannelOrderTime(), dateFormat));
        orderPageVo.setTimeDifference(DateUtil.between(orders.getCreateTime(), new Date(), DateUnit.HOUR));

        if (ObjectUtil.isNotNull(orders.getOrderState())) {
            List<OrderStateType> orderStatusTypes = new ArrayList<>();
            orderStatusTypes.add(OrderStateType.UnPaid);
            orderStatusTypes.add(OrderStateType.Failed);
            orderStatusTypes.add(OrderStateType.Canceled);
            orderStatusTypes.add(OrderStateType.Refunded);
            orderStatusTypes.add(OrderStateType.Pending);
            if (orderStatusTypes.contains(orders.getOrderState())) {
                orderPageVo.setOrderStatus(orders.getOrderState().name());
            } else if (ObjectUtil.isNotNull(orders.getFulfillmentProgress())) {
                orderPageVo.setOrderStatus(orders.getFulfillmentProgress().name());
            }

        }
        if (ObjectUtil.isNotNull(orders.getFulfillmentProgress())) {
            orderPageVo.setFulfillment(orders.getFulfillmentProgress().name());
        }

        orderPageVo.setPayFailedMessage(orders.getPayErrorMessage());
        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderId(orders.getId());
        if (ObjectUtil.isNotNull(logisticsInfo)) {
            if (ObjectUtil.isNotNull(logisticsInfo.getLogisticsType())) {
                orderPageVo.setLogisticsType(logisticsInfo.getLogisticsType().name());
            }
        }

        if (WMSFailMessage.length() > 0) {
            orderPageVo.setFulfillmentFailMessage_zh_CN(WMSFailMessage.toString());
            orderPageVo.setFulfillmentFailMessage_en_US(WMSFailMessage.toString());
        }
        OrderAddressInfo orderAddressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orders.getOrderNo(), OrderAddressType.ShipAddress);
        orderPageVo.setShipTo(orderSupport.addressToBody(orderAddressInfo));

        //获取订单退款信息
        LambdaQueryWrapper<OrderRefund> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefund::getOrderNo,orders.getOrderNo());
        queryWrapper.eq(OrderRefund::getRefundState,OrderRefundStateType.Refunded);
        queryWrapper.eq(OrderRefund::getRefundAmountState, RefundAmountStateEnum.Refunded);
        List<OrderRefund> orderRefunds =TenantHelper.ignore(()->iOrderRefundService.getBaseMapper().selectList(queryWrapper)) ;
        if (Objects.equals(tenantType, TenantType.Supplier)) {
            //销售额金额：
            orderPageVo.setTotalAmount(String.valueOf(orders.getOriginalActualTotalAmount()));
            //原始应付总金额（供货商）
            orderPageVo.setProductAmount(String.valueOf(orders.getOriginalPayableTotalAmount()));
            //原始操作费总金额（供货商）
            orderPageVo.setOperationFee(String.valueOf(orders.getOriginalTotalOperationFee()));
            //原始尾程派送费总额
            orderPageVo.setFinalDeliveryFee(String.valueOf(orders.getOriginalTotalFinalDeliveryFee()));
            //实付金额：
            orderPageVo.setTotal(String.valueOf(orders.getOriginalActualTotalAmount()));
            //定金original_prepaid_total_amount
            orderPageVo.setTotalDeposit(String.valueOf(orders.getOriginalPrepaidTotalAmount()));
            //售后金额original_refund_executable_amount
            orderPageVo.setRefundAmount(String.valueOf(orderRefunds.stream()
                                                                   .map(OrderRefund::getOriginalRefundAmount)
                                                                   .reduce(BigDecimal.ZERO, BigDecimal::add)));

        } else {
            //实付金额：
            orderPageVo.setTotal(String.valueOf(orders.getOriginalActualTotalAmount()));
            //应付金额
            orderPageVo.setProductAmount(String.valueOf(orders.getOriginalActualTotalAmount()));
            //销售额金额：
            orderPageVo.setTotalAmount(String.valueOf(orders.getPlatformActualTotalAmount()));
            //原始操作费总金额（供货商）
            orderPageVo.setOperationFee(String.valueOf(orders.getPlatformTotalOperationFee()));
            //原始尾程派送费总额
            orderPageVo.setFinalDeliveryFee(String.valueOf(orders.getPlatformTotalFinalDeliveryFee()));
            //定金original_prepaid_total_amount
            orderPageVo.setTotalDeposit(String.valueOf(orders.getPlatformPrepaidTotalAmount()));
            //售后金额original_refund_executable_amount
            orderPageVo.setRefundAmount(String.valueOf(orderRefunds.stream()
                                                                   .map(OrderRefund::getPlatformRefundAmount)
                                                                   .reduce(BigDecimal.ZERO, BigDecimal::add)));

        }

        //TODO 订单列表履约错误信息设置
        /*List<ShippingOrders> addressException =
            shippingOrdersRepository.getErrorShippingOrders(orders.getId(), "addressException");
        if (CollectionUtils.isNotEmpty(addressException) && ObjectUtil.notEqual(storeType, StoreType.Sup)) {
            orderPageVo.setFulfillmentFailMessage_zh_CN("存在地址错误的四海发货单，请进入订单详情编辑收货地址以重新创建四海发货单。");
            orderPageVo.setFulfillmentFailMessage_en_US("Bizark sale order occurs address exception. Please go to Order "
                + "Detail to edit the shipping address to recreate the Bizark sale order.");
        }

        List<ShippingOrders> insufficientInventoryException =
            shippingOrdersRepository.getErrorShippingOrders(orders.getId(), "insufficientInventoryException");
        if (CollectionUtils.isNotEmpty(insufficientInventoryException) && ObjectUtil.notEqual(storeType, StoreType.Bulk)) {
            orderPageVo.setFulfillmentFailMessage_zh_CN("四海仓库库存不足");
            orderPageVo.setFulfillmentFailMessage_en_US("Insufficient inventory in Bizark");
        }*/
        return orderPageVo;
    }

    /**
     * 构建订单详情
     *
     * @param orders
     * @return
     */
    @Override
    public OrderDetailVo buildOrderDetail(Orders orders) {

        OrderDetailVo detailVo = new OrderDetailVo();
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        log.info("查询订单详情："+orders.getOrderNo()+"租户类型"+tenantType);
        String tenantId = LoginHelper.getTenantId();

        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderId(orders.getId());
        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orders.getId());
        OrderItem item = orderItems.get(0);
        String dTenantId = item.getTenantId();
        List<OrderItemDetailVo> detailVoList = orderItemDetailBuild(orderItems, logisticsInfo);

        String orderNo = orders.getOrderNo();
        OrderAddressInfo orderAddressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);
        String phoneNumber = orderAddressInfo.getPhoneNumber();
        ChannelTypeEnum channelType = orders.getChannelType();
        Date channelOrderTime = orders.getChannelOrderTime();
        OrderStateType orderStatus = orders.getOrderState();
        LogisticsTypeEnum logisticsType = orders.getLogisticsType();

        OrderPageVo orderPageVo = new OrderPageVo();
        orderPageVo.setActivityCode(orders.getActivityCode());
        orderPageVo.setActivityType(orders.getActivityType());
        orderPageVo.setFulfillmentProgress(orders.getFulfillmentProgress().name());
        orderPageVo.setCancelStatus(orders.getCancelStatus());
        orderPageVo.setCurrency(orders.getCurrency());
        orderPageVo.setCurrencySymbol(orders.getCurrencySymbol());
        orderPageVo.setOrderId(orders.getOrderNo());
        orderPageVo.setChannelOrderTime(orders.getChannelOrderTime());
        if(ObjectUtil.isEmpty(orders.getLatestDeliveryTime())){
            orderPageVo.setLatestDeliveryTime("-");
        }else {
            String format = DateUtil.format(orders.getLatestDeliveryTime(), "yyyy-MM-dd HH:mm");
            orderPageVo.setLatestDeliveryTime(format);
        }

        orderPageVo.setShipmentException(orders.getShipmentException());
        if (StringUtils.isNotBlank(phoneNumber)) {
            orderPageVo.setPhoneNumber(phoneNumber);
        } else {
            phoneNumber = "";
        }

        // 处理地址信息
        OrderAddressType addressType = orderAddressInfo.getAddressType();
        if(isSpecialForErp(dTenantId,tenantId)){
            if (ObjectUtil.equals(addressType, OrderAddressType.ShipAddress)) {
                orderPageVo.setShipTo(orderSupport.addressToBodyForErp(orderAddressInfo));
            }
            if (ObjectUtil.equals(addressType, OrderAddressType.BillAddress)) {
                orderPageVo.setBillTo(orderSupport.addressToBodyForErp(orderAddressInfo));
            }
        }else {
            if (ObjectUtil.equals(addressType, OrderAddressType.ShipAddress)) {
                orderPageVo.setShipTo(orderSupport.addressToBody(orderAddressInfo));
            }
            if (ObjectUtil.equals(addressType, OrderAddressType.BillAddress)) {
                orderPageVo.setBillTo(orderSupport.addressToBody(orderAddressInfo));
            }
        }
        orderPageVo.setChannelAlias(orders.getChannelAlias());
//        if (StrUtil.isBlank(orders.getChannelAlias())) {
//            TenantSalesChannelVo tenantSalesChannelVo = iTenantSalesChannelService.queryById(orders.getChannelId());
//            if (tenantSalesChannelVo != null) {
//                orderPageVo.setChannelAlias(tenantSalesChannelVo.getChannelAlias());
//            }
//        }


        //处理渠道订单时间
        if (ObjectUtil.isNotNull(channelOrderTime)) {
            String channelTimeStr = DateUtil.format(channelOrderTime, dateFormat);
            orderPageVo.setOrderChannelTime(channelTimeStr);
        }

        orderPageVo.setLogisticsType(logisticsType.name());
        orderPageVo.setLogisticsAccount(StrUtil.emptyToNull(logisticsInfo.getLogisticsAccount()));
        orderPageVo.setLogisticsAccountZipCode(StrUtil.emptyToNull(logisticsInfo.getLogisticsAccountZipCode()));
        orderPageVo.setZipCode(StrUtil.emptyToNull(logisticsInfo.getZipCode()));
        orderPageVo.setLogisticsServiceName(StrUtil.emptyToNull(logisticsInfo.getLogisticsServiceName()));
        if (ObjectUtil.isNotNull(channelType)){
        orderPageVo.setChannelType(channelType.name());
        }
        orderPageVo.setChannelOrderId(orders.getChannelOrderName());
        IntactAddressInfoVo billTo = orderPageVo.getBillTo();
        orderPageVo.setBillingAddress(ObjectUtil.isNotNull(billTo) ? billTo.getIntactAddress() : null);
        orderPageVo.setContactInformation(phoneNumber);
        orderPageVo.setCustomerName(orders.getTenantId());
        orderPageVo.setNotes(orders.getOrderNote());
        orderPageVo.setFulfillment(orders.getFulfillmentProgress().name());
        if (ObjectUtil.isNotNull(orderStatus)) {
            List<OrderStateType> orderStatusTypes = new ArrayList<>();
            orderStatusTypes.add(OrderStateType.UnPaid);
            orderStatusTypes.add(OrderStateType.Failed);
            orderStatusTypes.add(OrderStateType.Canceled);
            orderStatusTypes.add(OrderStateType.Refunded);
            orderStatusTypes.add(OrderStateType.Pending);
            if (orderStatusTypes.contains(orderStatus)) {
                orderPageVo.setOrderStatus(orderStatus.name());
            } else if (ObjectUtil.isNotNull(orders.getFulfillmentProgress())) {
                orderPageVo.setOrderStatus(orders.getFulfillmentProgress().name());
            }

        }
        if (ObjectUtil.isNotEmpty(orders.getChannelId())){
            TenantSalesChannel channel = TenantHelper.ignore(() -> iTenantSalesChannelService.getBaseMapper()
                                                                                             .selectById(orders.getChannelId()));
            if (ObjectUtil.isNotEmpty(channel)){
                orderPageVo.setChannelAlias(channel.getChannelName());
                orderPageVo.setChannelType(channel.getChannelType());
            }
        }else {
            orderPageVo.setChannelAlias(orders.getChannelAlias());
        }



        for (OrderItemDetailVo orderItemCategoryBody : detailVoList) {
            OrderItemVo orderItemVo = orderItemCategoryBody.getOrderItem();
            Boolean canConfirmReceipt = orderItemVo.getCanConfirmReceipt();
            if (canConfirmReceipt) {
                orderPageVo.setCanConfirmReceipt(canConfirmReceipt);
            }
        }

        if (!Objects.equals(tenantType, TenantType.Supplier)) {
            if (Objects.equals(tenantType, TenantType.Distributor)) {
                String logisticsCompanyName = logisticsInfo.getLogisticsCompanyName();
                String logisticsAccount = logisticsInfo.getLogisticsAccount();
                if ((ObjectUtil.equals(channelType, ChannelTypeEnum.Others)||ObjectUtil.equals(channelType, ChannelTypeEnum.TikTok) ||ObjectUtil.equals(channelType, ChannelTypeEnum.Temu) || ObjectUtil.equals(channelType, ChannelTypeEnum.Amazon_VC)
                    ||ObjectUtil.equals(channelType, ChannelTypeEnum.SC)||ObjectUtil.equals(channelType, ChannelTypeEnum.VC_DF))
                    && StringUtils.isNotBlank(logisticsCompanyName) && StringUtils.isBlank(logisticsAccount) &&
                    (ObjectUtil.equals(orderStatus, OrderStateType.UnPaid) || ObjectUtil.equals(orderStatus, OrderStateType.Failed))) {
                    detailVo.setCanUploadLabel(true);
                }


            }
        }
//        OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
//        if (orderAttachment != null) {
//            orderPageVo.setAttachmentShowUrl(orderAttachment.getAttachmentShowUrl());
//            orderPageVo.setAttachmentName(orderAttachment.getAttachmentOriginalName());
//        }
//
//        //快递标签
//        if (logisticsInfo.getShippingLabelExist()) {
//            OrderAttachment shippingLabel = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
//            if (shippingLabel != null) {
//                detailVo.setHasLabel(true);
//                String showUrl = shippingLabel.getAttachmentShowUrl();
//                detailVo.setShippingLabelShowUrl(showUrl);
//                String attachmentName = shippingLabel.getAttachmentOriginalName();
//                if (StringUtils.isNotBlank(attachmentName)) {
//                    attachmentName = StrUtil.addSuffixIfNot(attachmentName, ".pdf");
//                    detailVo.setLabelFileName(attachmentName);
//                } else {
//                    detailVo.setLabelFileName(orderNo + ".pdf");
//                }
//            }
//        }
        LambdaQueryWrapper<OrderAttachment> oo = new LambdaQueryWrapper<>();
        oo.eq(OrderAttachment::getOrderNo,orders.getOrderNo());
        List<OrderAttachment> orderAttachments = iOrderAttachmentService.getBaseMapper().selectList(oo);
        List<OrderDetailsAttachmentVo> orderDetailsAttachmentVos = BeanUtil.copyToList(orderAttachments, OrderDetailsAttachmentVo.class);
        detailVo.setOrderDetailsAttachmentVos(orderDetailsAttachmentVos);


        //获取订单退款信息
        LambdaQueryWrapper<OrderRefund> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefund::getOrderNo,orders.getOrderNo());
        queryWrapper.eq(OrderRefund::getRefundState,OrderRefundStateType.Refunded);
        queryWrapper.eq(OrderRefund::getRefundAmountState, RefundAmountStateEnum.Refunded);
        List<OrderRefund> orderRefunds = iOrderRefundService.getBaseMapper().selectList(queryWrapper);
        if (Objects.equals(tenantType, TenantType.Supplier.name())) {
            //销售额金额：
            orderPageVo.setTotalAmount(String.valueOf(orders.getOriginalActualTotalAmount()));
            //原始应付总金额（供货商）
            orderPageVo.setProductAmount(String.valueOf(orders.getOriginalPayableTotalAmount()));
            //原始操作费总金额（供货商）
            orderPageVo.setOperationFee(String.valueOf(orders.getOriginalTotalOperationFee()));
            //原始尾程派送费总额
            orderPageVo.setFinalDeliveryFee(String.valueOf(orders.getOriginalTotalFinalDeliveryFee()));
            //实付金额：
            orderPageVo.setTotal(String.valueOf(orders.getOriginalActualTotalAmount()));
            //定金original_prepaid_total_amount
            orderPageVo.setTotalDeposit(String.valueOf(orders.getOriginalPrepaidTotalAmount()));
            //售后金额original_refund_executable_amount
            orderPageVo.setRefundAmount(String.valueOf(orderRefunds.stream()
                                                                   .map(OrderRefund::getOriginalRefundAmount)
                                                                   .reduce(BigDecimal.ZERO, BigDecimal::add)));

        } else {
            //实付金额：
            orderPageVo.setTotal(String.valueOf(orders.getOriginalActualTotalAmount()));
            //应付金额
            orderPageVo.setProductAmount(String.valueOf(orders.getOriginalActualTotalAmount()));
            //销售额金额：
            orderPageVo.setTotalAmount(String.valueOf(orders.getPlatformActualTotalAmount()));
            //原始操作费总金额（供货商）
            orderPageVo.setOperationFee(String.valueOf(orders.getPlatformTotalOperationFee()));
            //原始尾程派送费总额
            orderPageVo.setFinalDeliveryFee(String.valueOf(orders.getPlatformTotalFinalDeliveryFee()));
            //定金original_prepaid_total_amount
            orderPageVo.setTotalDeposit(String.valueOf(orders.getPlatformPrepaidTotalAmount()));
            //售后金额original_refund_executable_amount
            orderPageVo.setRefundAmount(String.valueOf(orderRefunds.stream()
                                                                   .map(OrderRefund::getPlatformRefundAmount)
                                                                   .reduce(BigDecimal.ZERO, BigDecimal::add)));

        }
        orderPageVo.setStartDate(DateUtil.format(orders.getCreateTime(), dateFormat));
        orderPageVo.setStartTime(DateUtil.format(orders.getCreateTime(), dateFormat));
        orderPageVo.setPayDate(DateUtil.format(orders.getPayTime(), dateFormat));
        if (ObjectUtil.isNotEmpty(orders.getChannelId())){
            TenantSalesChannel channel = TenantHelper.ignore(() -> iTenantSalesChannelService.getBaseMapper()
                                                                                            .selectById(orders.getChannelId()));
            if (ObjectUtil.isNotEmpty(channel)){
                orderPageVo.setChannelAlias(channel.getChannelName());
                orderPageVo.setChannelType(channel.getChannelType());
            }
        }else {
            orderPageVo.setChannelAlias(orders.getChannelAlias());
        }
        BigDecimal refund = iOrderRefundService.sumByRefundPrice(orders.getId(), ObjectUtil.equals(tenantType, TenantType.Supplier));
        if (refund != null) {
            orderPageVo.setRefundAmount(DecimalUtil.bigDecimalToString(refund));
        }
        //封装仓库信息,后期需要走仓库映射关系。
        String warehouseInfo=null;
        OrderItemProductSku byOrderItemId = iOrderItemProductSkuService.getByOrderItemId(orderItems.get(0).getId());
        if (ObjectUtil.isNotNull(byOrderItemId)){
            if (StrUtil.isNotBlank(byOrderItemId.getWarehouseSystemCode())){
                warehouseInfo= warehouseService.getWarehouseAddressInfo(byOrderItemId.getWarehouseSystemCode());
            }
        }
        //erp渠道订单不显示仓库
        if (ChannelTypeEnum.Erp.equals(orders.getChannelType())&&isSpecialForErp(dTenantId,tenantId)) {
            warehouseInfo = null;
        }
        orderPageVo.setWarehouseInfo(warehouseInfo);
        detailVo.setOrderItemsCategory(detailVoList);
        detailVo.setOrderBody(orderPageVo);

        return detailVo;
    }

    /**
     * 功能描述：专门用于erp
     *
     * @param dTenantId d租户id
     * @param tenantId  租户id
     * @return boolean
     * <AUTHOR>
     * @date 2025/04/11
     */
    private boolean isSpecialForErp(String dTenantId, String tenantId) {
        if (StrUtil.isNotBlank(dTenantId) && StrUtil.isNotBlank(tenantId)) {
            // 当前登录用户是D4ZQGHV 价格特殊处理,返回true ;当前登陆用户是S0BJHUA,查看的订单的详情页是D4ZQGHV的订单，返回true
            if(("D4ZQGHV".equals(tenantId))||("S0BJHUA".equals(tenantId)&&"D4ZQGHV".equals(dTenantId))){
                return true;
            }
        }
        return false;
    }

    /**
     * 子订单详情构建
     * @param orderItems
     * @param logisticsInfo
     * @return
     */
    private List<OrderItemDetailVo> orderItemDetailBuild(List<OrderItem> orderItems, OrderLogisticsInfo logisticsInfo) {
        List<OrderItemDetailVo> detailVoList = new ArrayList<>();
        Long orderId = logisticsInfo.getOrderId();
        OrderItem item = orderItems.get(0);

        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        String tenantId = LoginHelper.getTenantId();
        String hours = businessParameterService.getValueFromString(BusinessParameterType.CONFIRM_DISPATCHED_HOURS);
        // 员工最大可以重复确认发货的时间（小时）
        Long maxHours = Long.parseLong(hours);

        for (OrderItem orderItem : orderItems) {
            String orderItemNo = orderItem.getOrderItemNo();
            String supplierTenantId = orderItem.getSupplierTenantId();
            String dTenantId = orderItem.getTenantId();
            OrderStateType orderItemState = orderItem.getOrderState();
            LogisticsProgress fulfillmentProgress = orderItem.getFulfillmentProgress();
            // 如果是供货商用户调用，则不是自己的商品就跳过
            if (Objects.equals(tenantType, TenantType.Supplier) && !StrUtil.equals(tenantId, supplierTenantId)) {
                continue;
            }

            OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemId(orderItem.getId());

            String carrier = logisticsInfo.getLogisticsCompanyName();

            Integer callingApi = 0;

            List<OrderItemTrackingRecord> trackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
            List<String> trackingNoList = new ArrayList<>();
            List<TrackingNoBo> trackingInfoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(trackingList)) {
                for (OrderItemTrackingRecord tracking : trackingList) {
                    carrier = tracking.getLogisticsCarrier();
                    trackingNoList.add(tracking.getLogisticsTrackingNo());
                    trackingInfoList.add(new TrackingNoBo(carrier, tracking.getLogisticsTrackingNo()));
                }
            }

            // 将各种状态组合成标签，每一个组合后的标签代表一个分组，展示在前端是一个订单区域
            OrderItemDetailVo detailVo = new OrderItemDetailVo();
            detailVo = new OrderItemDetailVo();
            detailVo.setFulfillment(fulfillmentProgress);
            detailVo.setCarrier(carrier);
            detailVo.setTrackingNo(CollUtil.join(trackingNoList,"\n"));
            detailVo.setTrackingInfoList(trackingInfoList);


            // 是否第三方物流账号
            String logisticsAccount = logisticsInfo.getLogisticsAccount();
            if (StringUtils.isNotBlank(logisticsAccount)) {
                detailVo.setLogisticsAccount(logisticsAccount);
            }

            // 是否显示确认发货按钮
            Boolean showDispatchedBtn = true;
            // 履约状态不是未发货，将禁用确认发货按钮
            if (ObjectUtil.notEqual(fulfillmentProgress, LogisticsProgress.UnDispatched)) {
                showDispatchedBtn = false;
                // 例外条件，如果已发货，且当前用户为员工账号，且确认发货在指定的时间内（目前48小时内），员工可以操作再次确认发货
                if (ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Dispatched) && ObjectUtil.equals(tenantType, TenantType.Manager)) {
                    Date dispatchedTime = orderItem.getDispatchedTime();
                    if (dispatchedTime != null) {
                        long betweenHours = DateUtil.between(dispatchedTime, new Date(), DateUnit.HOUR);
                        if (NumberUtil.isLess(NumberUtil.toBigDecimal(betweenHours), NumberUtil.toBigDecimal(maxHours))) {
                            showDispatchedBtn = true;
                        }
                    }
                }
            }
            detailVo.setShowBtn(showDispatchedBtn);

            // 处理子订单数据
            OrderItemVo orderItemBody = new OrderItemVo();
            Orders orders = new Orders();
            try{
                orders = TenantHelper.ignore(() -> iOrdersService.getById(orderId));
                orderItemBody.setCurrency(orders.getCurrency());
            }catch (Exception e){
                log.error("币种信息获取失败",e);
            }

            Integer totalQuantity = orderItem.getTotalQuantity();
            if(totalQuantity.equals(0)&&ChannelTypeEnum.TikTok.name().equals(item.getChannelType().name())){
                totalQuantity =1;
            }
            //商品总价（供应商）
            BigDecimal originalPayableTotalAmount = orderItem.getOriginalPayableTotalAmount();
            //已预付总价（供应商）
            BigDecimal originalPrepaidTotalAmount = orderItem.getOriginalPrepaidTotalAmount();
            //实际总价（供应商）
            BigDecimal originalActualTotalAmount = orderItem.getOriginalActualTotalAmount();
            //商品总价（平台）
            BigDecimal platformPayableTotalAmount = orderItem.getPlatformPayableTotalAmount();
            //已预付总价（平台）
            BigDecimal platformPrepaidTotalAmount = orderItem.getPlatformPrepaidTotalAmount();
            //实际总价（平台）
            BigDecimal platformActualTotalAmount = orderItem.getPlatformActualTotalAmount();

            OrderProductSkuBo orderProductSkuBody = new OrderProductSkuBo();
            OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
            if (ObjectUtil.isNotNull(orderItemProductSku)){
                String productName = orderItemProductSku.getProductName();
                String warehouseCode;
                if (ChannelTypeEnum.Erp.equals(orders.getChannelType())&&isSpecialForErp(dTenantId,tenantId)) {
                    warehouseCode = null;
                } else {
                    warehouseCode = orderItemProductSku.getWarehouseSystemCode();
                }
                orderProductSkuBody.setSku(orderItemProductSku.getSku());
                orderProductSkuBody.setItemNo(orderItemProductSku.getProductSkuCode());
                orderProductSkuBody.setWarehouseSystemCode(orderItemProductSku.getWarehouseSystemCode());
                orderProductSkuBody.setImgUrl(orderItemProductSku.getImageShowUrl());
                orderItemBody.setImageShowUrl(orderItemProductSku.getImageShowUrl());
                orderItemBody.setProductName(productName);
                detailVo.setWarehouseCode(warehouseCode);
            }
            orderProductSkuBody.setSalesChannelOrderId(orderItemNo);

            orderItemBody.setOrderStatus(orderItem.getOrderState().name());

            orderItemBody.setNum(totalQuantity);
            orderItemBody.setOrderId(orderItemNo);
            orderItemBody.setOrderItemNo(orderItemNo);
            orderItemBody.setProductSku(orderProductSkuBody);

            if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
               // orderItemBody.setUnitPrice(orderItem.getOriginalPayableUnitPrice());
                //取价改为订单应付金额
                if (ObjectUtil.isNotNull(orders.getOriginalPayableTotalAmount())){
                    orderItemBody.setUnitPrice(orders.getOriginalPayableTotalAmount().divide(BigDecimal.valueOf(orders.getTotalQuantity()),2, RoundingMode.HALF_UP));
                }
                // 如果是香港hj or zjhj 都显示销售价
                if("D4ZQGHV".equals(orders.getTenantId())||"S0BJHUA".equals(LoginHelper.getTenantId())){
                    orderItemBody.setProductTotalPrice(platformActualTotalAmount);
                }else {
                    orderItemBody.setProductTotalPrice(originalPayableTotalAmount);
                }

                if (ObjectUtil.isNotNull(orderItemPrice)){
                    orderItemBody.setProductUnitPrice(NumberUtil.mul(orderItemPrice.getOriginalUnitPrice(), totalQuantity));
                    orderItemBody.setOperationFee(NumberUtil.mul(orderItemPrice.getOriginalOperationFee(), totalQuantity));
                    orderItemBody.setFinalDeliveryFee(NumberUtil.mul(orderItemPrice.getOriginalFinalDeliveryFee(), totalQuantity));
                }

                orderItemBody.setTotalPrice(originalActualTotalAmount);
                orderItemBody.setDepositTotalPrice(originalPrepaidTotalAmount);
            } else {
            // 运营侧要求将分销商的产品金额相关都更改为供应商的金额
        //        orderItemBody.setUnitPrice(orderItem.getPlatformPayableUnitPrice());
         //       orderItemBody.setUnitPrice(orderItem.getOriginalPayableUnitPrice());
                if (ObjectUtil.isNotNull(orders.getOriginalActualTotalAmount())){
                    orderItemBody.setUnitPrice(orders.getOriginalActualTotalAmount().divide(BigDecimal.valueOf(orders.getTotalQuantity()),2, RoundingMode.HALF_UP));
                }
                if("D4ZQGHV".equals(orders.getTenantId())||"S0BJHUA".equals(LoginHelper.getTenantId())){
                    orderItemBody.setProductTotalPrice(platformActualTotalAmount);
                }else {
                    orderItemBody.setProductTotalPrice(originalPayableTotalAmount);
                }
                // 改造为商品单价+操作费+尾程费
                // 这里的应付金额是 所有的相加
                // tag lty
                if (ObjectUtil.isNotNull(orderItemPrice)){
                    BigDecimal operationFees = NumberUtil.mul(orderItemPrice.getOriginalOperationFee(), totalQuantity);
                    orderItemBody.setOperationFee(operationFees);
                    BigDecimal finalDeliveryFees = NumberUtil.mul(orderItemPrice.getOriginalFinalDeliveryFee(), totalQuantity);
                    orderItemBody.setFinalDeliveryFee(finalDeliveryFees);
                    BigDecimal unitPrices = NumberUtil.mul(orderItemPrice.getOriginalUnitPrice(), totalQuantity);
                    orderItemBody.setProductUnitPrice(unitPrices);
                }

                // tag lty
//                BigDecimal unitPrices = NumberUtil.mul(orderItemPrice.getOriginalUnitPrice(), totalQuantity).add(operationFees).add(finalDeliveryFees);
//                orderItemBody.setProductUnitPrice(NumberUtil.mul(orderItemPrice.getPlatformUnitPrice(), totalQuantity));
//                orderItemBody.setOperationFee(NumberUtil.mul(orderItemPrice.getPlatformOperationFee(), totalQuantity));
//                orderItemBody.setFinalDeliveryFee(NumberUtil.mul(orderItemPrice.getPlatformFinalDeliveryFee(), totalQuantity));
                orderItemBody.setTotalPrice(platformActualTotalAmount);
                orderItemBody.setDepositTotalPrice(platformPrepaidTotalAmount);
            }

            Date dispatchedTime = orderItem.getDispatchedTime();
            if (ObjectUtil.isNotNull(dispatchedTime)) {
                orderItemBody.setDeliveryTime(DateUtil.format(dispatchedTime, dateFormat));
            }

            // 是否可以申请退款
            Boolean canRefund;
            OrderRefund orderRefund = null;
            OrderRefundItem lastRefundItem = CollUtil.get(iOrderRefundItemService.queryListByOrderItemId(orderItem.getId()), 0);

            // 查询最近一条退款记录
            if (lastRefundItem == null) {
                canRefund = true;
            } else {
                orderRefund = iOrderRefundService.queryByIdNotTenant(lastRefundItem.getOrderRefundId());
                if (Objects.equals(orderItemState, OrderStateType.Paid)) {
                    canRefund = orderRefundRuleService.compareRefundRules(orderItem, true);
                } else {
                    canRefund = false;
                }
            }

            // 是否可以申请退款
            orderItemBody.setCanRefund(canRefund);
            // 售后可执行金额
            orderItemBody.setRefundExecutableAmount(orderItem.getPlatformRefundExecutableAmount());

            // 打包售后信息
            packageRefund:
            {
                if (orderRefund != null) {
                    orderItemBody.setOrderRefundNo(orderRefund.getOrderRefundNo());
                    OrderRefundStateType refundStatus = orderRefund.getRefundState();
                    if (ObjectUtil.equals(refundStatus, OrderRefundStateType.Reject)) {
                        orderItemBody.setRefundStatus(OrderRefundStateType.Reject.name());
                    } else if (ObjectUtil.equals(refundStatus, OrderRefundStateType.Refunded)) {
                        orderItemBody.setRefundStatus(OrderRefundStateType.Refunded.name());
                    } else if (ObjectUtil.equals(refundStatus, OrderRefundStateType.Canceled)) {
                        break packageRefund;
                    } else {
                        orderItemBody.setRefundStatus(OrderRefundStateType.Refunding.name());
                    }

                    String refundStatusStr = orderItemBody.getRefundStatus();
                    String orderRefundNo = orderRefund.getOrderRefundNo();
                    Date refundApplyTime = orderRefund.getRefundApplyTime();
                    String refundRuleReason = orderRefund.getRefundRuleReason();
                    BigDecimal platformRefundAmount = orderRefund.getPlatformRefundAmount();
                    String managerReviewOpinion = orderRefund.getManagerReviewOpinion();
                    String supplierReviewOpinion = orderRefund.getSupplierReviewOpinion();
                    Date managerReviewTime = orderRefund.getManagerReviewTime();
                    Date supplierReviewTime = orderRefund.getSupplierReviewTime();

                    OrderRefundItemBody refundInfo = new OrderRefundItemBody();
                    refundInfo.setOrderRefundNo(orderRefundNo);
                    refundInfo.setOrderRefundItemNo(lastRefundItem.getOrderRefundItemNo());
                    refundInfo.setRefundReason(refundRuleReason);
                    refundInfo.setRefundPrice(DecimalUtil.bigDecimalToString(platformRefundAmount));
                    refundInfo.setApplicationTime(DateUtil.format(refundApplyTime, dateFormat));
                    refundInfo.setRefundStatus(refundStatusStr);
                    refundInfo.setHandleTime(DateUtil.format(supplierReviewTime, dateFormat));

                    if (supplierReviewTime != null) {
                        refundInfo.setHandleTime(DateUtil.format(supplierReviewTime, dateFormat));
                    } else if (managerReviewTime != null) {
                        refundInfo.setHandleTime(DateUtil.format(managerReviewTime, dateFormat));
                    }

                    if (OrderRefundStateType.Reject.name().equals(refundStatusStr)) {
                        if (StrUtil.isNotBlank(supplierReviewOpinion)) {
                            refundInfo.setVerifyOpinion(supplierReviewOpinion);
                        } else if (StrUtil.isNotBlank(managerReviewOpinion)) {
                            refundInfo.setVerifyOpinion(managerReviewOpinion);
                        }
                    }
                    orderItemBody.setRefundInfo(refundInfo);
                }
            }

            // 是否可以确认收货
            orderItemBody.setCanConfirmReceipt(ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Dispatched) && ObjectUtil.equals(orderItemState, OrderStateType.Paid));
            detailVo.addOrderItem(orderItemBody);
            detailVoList.add(detailVo);
        }
        return detailVoList;
    }


    /**
     * 验证订单类型
     *
     * @param orderType
     * @return
     */
    @Override
    public boolean isThisImpl(OrderType orderType) {
        return OrderType.Normal.equals(orderType);
    }

    @Override
    public OrderDetailVo buildOrderDetailForExport(Orders orders) {
        OrderDetailVo detailVo = new OrderDetailVo();
        TenantType tenantType = LoginHelper.getTenantTypeEnum();


        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderId(orders.getId());
        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orders.getId());

        List<OrderItemDetailVo> detailVoList = orderItemDetailBuild(orderItems, logisticsInfo);

        String orderNo = orders.getOrderNo();
        OrderAddressInfo orderAddressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);
        String phoneNumber = orderAddressInfo.getPhoneNumber();
        ChannelTypeEnum channelType = orders.getChannelType();

        OrderStateType orderStatus = orders.getOrderState();
        LogisticsTypeEnum logisticsType = orders.getLogisticsType();

        OrderPageVo orderPageVo = new OrderPageVo();
        orderPageVo.setOrderId(orders.getOrderNo());

        if (StringUtils.isNotBlank(phoneNumber)) {
            orderPageVo.setPhoneNumber(phoneNumber);
        } else {
            phoneNumber = "";
        }

        // 处理地址信息
        OrderAddressType addressType = orderAddressInfo.getAddressType();
        if (ObjectUtil.equals(addressType, OrderAddressType.ShipAddress)) {
            orderPageVo.setShipTo(orderSupport.addressToBody(orderAddressInfo));
        }
//        if (ObjectUtil.equals(addressType, OrderAddressType.BillAddress)) {
//            orderPageVo.setBillTo(orderSupport.addressToBody(orderAddressInfo));
//        }
        orderPageVo.setChannelAlias(orders.getChannelAlias());
        if (StrUtil.isBlank(orders.getChannelAlias())) {
            if(ObjectUtil.isNotEmpty(orders.getChannelId())){
                TenantSalesChannelVo tenantSalesChannelVo = iTenantSalesChannelService.queryById(orders.getChannelId());
                if (tenantSalesChannelVo != null) {
                    orderPageVo.setChannelAlias(tenantSalesChannelVo.getChannelAlias());
                }
            }

        }


        //处理渠道订单时间
//        if (ObjectUtil.isNotNull(channelOrderTime)) {
//            String channelTimeStr = DateUtil.format(channelOrderTime, dateFormat);
//            orderPageVo.setOrderChannelTime(channelTimeStr);
//        }

        orderPageVo.setLogisticsType(logisticsType.name());
        orderPageVo.setLogisticsAccount(StrUtil.emptyToNull(logisticsInfo.getLogisticsAccount()));
        orderPageVo.setLogisticsAccountZipCode(StrUtil.emptyToNull(logisticsInfo.getLogisticsAccountZipCode()));
        orderPageVo.setZipCode(StrUtil.emptyToNull(logisticsInfo.getZipCode()));
        orderPageVo.setLogisticsServiceName(StrUtil.emptyToNull(logisticsInfo.getLogisticsServiceName()));

        orderPageVo.setChannelType(channelType.name());
        orderPageVo.setChannelOrderId(orders.getChannelOrderName());
//        IntactAddressInfoVo billTo = orderPageVo.getBillTo();
//        orderPageVo.setBillingAddress(ObjectUtil.isNotNull(billTo) ? billTo.getIntactAddress() : null);
//        orderPageVo.setContactInformation(phoneNumber);
//        orderPageVo.setCustomerName(orders.getTenantId());
        orderPageVo.setNotes(orders.getOrderNote());
//        orderPageVo.setFulfillment(orders.getFulfillmentProgress().name());
//        if (ObjectUtil.isNotNull(orderStatus)) {
//            List<OrderStateType> orderStatusTypes = new ArrayList<>();
//            orderStatusTypes.add(OrderStateType.UnPaid);
//            orderStatusTypes.add(OrderStateType.Failed);
//            orderStatusTypes.add(OrderStateType.Canceled);
//            orderStatusTypes.add(OrderStateType.Refunded);
//            orderStatusTypes.add(OrderStateType.Pending);
//            if (orderStatusTypes.contains(orderStatus)) {
//                orderPageVo.setOrderStatus(orderStatus.name());
//            } else if (ObjectUtil.isNotNull(orders.getFulfillmentProgress())) {
//                orderPageVo.setOrderStatus(orders.getFulfillmentProgress().name());
//            }
//
//        }

//        OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
//        if (orderAttachment != null) {
//            orderPageVo.setAttachmentShowUrl(orderAttachment.getAttachmentShowUrl());
//            orderPageVo.setAttachmentName(orderAttachment.getAttachmentOriginalName());
//        }

        // 应付总金额
        BigDecimal payableTotalAmount = BigDecimal.ZERO;
        // 实付总金额
        BigDecimal actualTotalAmount = BigDecimal.ZERO;
        // 商品单价（不包含操作费与尾程费的）
        BigDecimal productUnitPrice = BigDecimal.ZERO;
        // 操作费
        BigDecimal operationFee = BigDecimal.ZERO;
        // 尾程派送费
        BigDecimal finalDeliveryFee = BigDecimal.ZERO;
        // 定金总金额
        BigDecimal depositTotalPrice = BigDecimal.ZERO;

        for (OrderItemDetailVo orderItemCategoryBody : detailVoList) {
            OrderItemVo orderItemVo = orderItemCategoryBody.getOrderItem();
            BigDecimal unitPrice = orderItemVo.getUnitPrice();
            if (Objects.equals(tenantType, TenantType.Supplier)) {
                payableTotalAmount = NumberUtil.add(payableTotalAmount, orderItemVo.getProductTotalPrice());
                actualTotalAmount = NumberUtil.add(actualTotalAmount, orderItemVo.getTotalPrice());
            }

            productUnitPrice = NumberUtil.add(productUnitPrice, orderItemVo.getProductUnitPrice());
            operationFee = NumberUtil.add(operationFee, orderItemVo.getOperationFee());
            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                finalDeliveryFee = NumberUtil.add(finalDeliveryFee, orderItemVo.getFinalDeliveryFee());
            }
            depositTotalPrice = NumberUtil.add(depositTotalPrice, orderItemVo.getDepositTotalPrice());

            Boolean canConfirmReceipt = orderItemVo.getCanConfirmReceipt();
            if (canConfirmReceipt) {
                orderPageVo.setCanConfirmReceipt(canConfirmReceipt);
            }
        }

        if (!Objects.equals(tenantType, TenantType.Supplier)) {
            payableTotalAmount = orders.getPlatformPayableTotalAmount();
            actualTotalAmount = orders.getPlatformActualTotalAmount();
            if (Objects.equals(tenantType, TenantType.Distributor)) {
                String logisticsCompanyName = logisticsInfo.getLogisticsCompanyName();
                String logisticsAccount = logisticsInfo.getLogisticsAccount();
                if ((ObjectUtil.equals(channelType, ChannelTypeEnum.Others)||ObjectUtil.equals(channelType, ChannelTypeEnum.TikTok) ||ObjectUtil.equals(channelType, ChannelTypeEnum.Temu) || ObjectUtil.equals(channelType, ChannelTypeEnum.Amazon_VC)
                    ||ObjectUtil.equals(channelType, ChannelTypeEnum.SC)||ObjectUtil.equals(channelType, ChannelTypeEnum.VC_DF))
                    && StringUtils.isNotBlank(logisticsCompanyName) && StringUtils.isBlank(logisticsAccount) &&
                    (ObjectUtil.equals(orderStatus, OrderStateType.UnPaid) || ObjectUtil.equals(orderStatus, OrderStateType.Failed))) {
                    detailVo.setCanUploadLabel(true);
                }

            }
        }

        //快递标签
//        if (logisticsInfo.getShippingLabelExist()) {
//            OrderAttachment shippingLabel = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
//            if (shippingLabel != null) {
//                detailVo.setHasLabel(true);
//                String showUrl = shippingLabel.getAttachmentShowUrl();
//                detailVo.setShippingLabelShowUrl(showUrl);
//                String attachmentName = shippingLabel.getAttachmentOriginalName();
//                if (StringUtils.isNotBlank(attachmentName)) {
//                    attachmentName = StrUtil.addSuffixIfNot(attachmentName, ".pdf");
//                    detailVo.setLabelFileName(attachmentName);
//                } else {
//                    detailVo.setLabelFileName(orderNo + ".pdf");
//                }
//            }
//        }

        orderPageVo.setTotalAmount(DecimalUtil.bigDecimalToString(payableTotalAmount));
        orderPageVo.setProductAmount(DecimalUtil.bigDecimalToString(productUnitPrice));
        orderPageVo.setOperationFee(DecimalUtil.bigDecimalToString(operationFee));
        orderPageVo.setFinalDeliveryFee(DecimalUtil.bigDecimalToString(finalDeliveryFee));
        orderPageVo.setTotal(DecimalUtil.bigDecimalToString(actualTotalAmount));
        orderPageVo.setTotalNumber(payableTotalAmount);
        orderPageVo.setTotalDeposit(DecimalUtil.bigDecimalToString(depositTotalPrice));
        orderPageVo.setStartDate(DateUtil.format(orders.getCreateTime(), dateFormat));
        orderPageVo.setPayDate(DateUtil.format(orders.getPayTime(), dateFormat));
        String orderTenantId = orders.getTenantId();
        orderPageVo.setDistributorId(orderTenantId);
        List<String> supplierCodes = orderItems.stream().map(OrderItem::getSupplierTenantId).filter(StrUtil::isNotBlank)
                                         .distinct().collect(Collectors.toList());

        // 将supplierCodes以逗号拼接成一个字符串
        if (CollUtil.isNotEmpty(supplierCodes)) {
            String supplierIds = String.join(",", supplierCodes);
            orderPageVo.setSupplierIds(supplierIds);
        }

        detailVo.setOrderItemsCategory(detailVoList);
        detailVo.setOrderBody(orderPageVo);

        return detailVo;
    }
}
